package com.example.habits9.ui.habitreorder

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSortType
import com.example.habits9.data.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

data class HabitReorderUiState(
    val habits: List<Habit> = emptyList(),
    val isLoading: Boolean = false,
    val isLocallyReordering: Boolean = false  // FIX: Track local reordering state
)

@HiltViewModel
class HabitReorderViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    // FIX: Add local state management for immediate UI updates like ManageSectionsScreen
    private val _uiState = MutableStateFlow(HabitReorderUiState(isLoading = true))
    val uiState: StateFlow<HabitReorderUiState> = _uiState.asStateFlow()

    init {
        // Initialize with repository data and keep it synchronized
        viewModelScope.launch {
            habitRepository.getAllHabits().collect { allHabits ->
                // Only update if we're not in the middle of a local reorder operation
                if (!_uiState.value.isLocallyReordering) {
                    val sortedHabits = allHabits.sortedWith(
                        compareBy<Habit> { if (it.customOrderIndex >= 0) it.customOrderIndex else Int.MAX_VALUE }
                            .thenBy { it.position }
                    )

                    _uiState.value = HabitReorderUiState(
                        habits = sortedHabits,
                        isLoading = false,
                        isLocallyReordering = false
                    )
                }
            }
        }
    }

    init {
        // Set sort type to custom order when entering this screen
        viewModelScope.launch {
            userPreferencesRepository.updateHabitSortType(HabitSortType.CUSTOM_ORDER)
        }
    }

    /**
     * FIX: Move habit with immediate local UI update, then persist to backend
     * Following the same pattern as ManageSectionsScreen.onSectionMoved
     */
    fun moveHabitAndSave(fromIndex: Int, toIndex: Int) {
        val currentHabits = _uiState.value.habits

        // Validate indices
        if (fromIndex < 0 || fromIndex >= currentHabits.size ||
            toIndex < 0 || toIndex >= currentHabits.size ||
            fromIndex == toIndex) {
            android.util.Log.d("HabitReorderViewModel", "moveHabitAndSave: Invalid indices - from=$fromIndex, to=$toIndex, size=${currentHabits.size}")
            return
        }

        android.util.Log.d("HabitReorderViewModel", "moveHabitAndSave: IMMEDIATE UI UPDATE $fromIndex -> $toIndex")

        // FIX: CRITICAL - Update local UI state FIRST, then persist to backend
        // This ensures the drag system immediately works with the new data order
        val mutableList = currentHabits.toMutableList()
        val item = mutableList.removeAt(fromIndex)
        mutableList.add(toIndex, item)

        // Update customOrderIndex for all habits to reflect new order
        val updatedHabits = mutableList.mapIndexed { index, habit ->
            habit.copy(customOrderIndex = index)
        }

        // FIX: Update UI state immediately for smooth UX (like ManageSectionsScreen)
        _uiState.value = _uiState.value.copy(
            habits = updatedHabits,
            isLocallyReordering = true  // Prevent repository updates from overriding
        )

        // Persist to backend asynchronously
        viewModelScope.launch {
            // Create new order mapping
            val habitOrderMap = updatedHabits.mapIndexed { index, habit ->
                habit.uuid to index
            }.toMap()

            val success = habitRepository.updateCustomOrderIndices(habitOrderMap)

            if (success) {
                // Also save to preferences as backup
                val habitUuids = updatedHabits.map { it.uuid }
                userPreferencesRepository.updateCustomHabitOrder(habitUuids)
                android.util.Log.d("HabitReorderViewModel", "moveHabitAndSave: BACKEND SAVE SUCCESSFUL")

                // Reset the locally reordering flag after successful save
                _uiState.value = _uiState.value.copy(isLocallyReordering = false)
            } else {
                android.util.Log.e("HabitReorderViewModel", "moveHabitAndSave: BACKEND SAVE FAILED")
                // Reset the locally reordering flag even on failure
                _uiState.value = _uiState.value.copy(isLocallyReordering = false)
            }
        }
    }

    /**
     * FORCE REFRESH: Force immediate UI state refresh from repository
     * This ensures the UI reflects the absolute latest data after drag operations
     */
    fun forceRefresh() {
        viewModelScope.launch {
            android.util.Log.d("HabitReorderViewModel", "FORCE REFRESH: Reloading habits from repository")

            // Force reload from repository to ensure we have the latest data
            val latestHabits = habitRepository.getAllHabitsOnce()
            val sortedHabits = latestHabits.sortedWith(
                compareBy<Habit> { if (it.customOrderIndex >= 0) it.customOrderIndex else Int.MAX_VALUE }
                    .thenBy { it.position }
            )

            _uiState.value = HabitReorderUiState(
                habits = sortedHabits,
                isLoading = false,
                isLocallyReordering = false
            )
        }
    }

    // REMOVED: discardChanges() - no longer needed with immediate persistence

    // REMOVED: saveOrder() methods - changes now persist immediately on drop

    // REMOVED: All saveOrder methods - immediate persistence only
}
