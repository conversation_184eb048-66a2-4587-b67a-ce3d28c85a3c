package com.example.habits9.data.analytics

import com.example.habits9.data.Completion
import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import com.example.habits9.utils.HabitScheduler
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for calculating habit analytics.
 * Contains all business logic for computing metrics, streaks, and chart data.
 */
@Singleton
class HabitAnalyticsUseCase @Inject constructor(
    private val habitRepository: HabitRepository,
    private val completionRepository: CompletionRepository
) {

    /**
     * Get current streak for a habit.
     * Counts consecutive days from today backwards where habit was completed and scheduled.
     */
    suspend fun getCurrentStreak(habitId: Long): Int {
        val habit = getHabitById(habitId) ?: return 0
        val completions = completionRepository.getCompletionsForHabit(habitId).first()
        
        var streak = 0
        var currentDate = LocalDate.now()
        
        while (true) {
            val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
            
            // Check if habit was scheduled for this day
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)
            if (!isScheduled) {
                // Skip non-scheduled days
                currentDate = currentDate.minusDays(1)
                continue
            }
            
            // Check if habit was completed on this day
            val isCompleted = isHabitCompletedOnDay(habit, completions, dayStart)
            
            if (isCompleted) {
                streak++
                currentDate = currentDate.minusDays(1)
            } else {
                // Streak is broken - habit was scheduled but not completed
                break
            }
        }
        
        return streak
    }

    /**
     * Get longest streak in habit's entire history.
     */
    suspend fun getLongestStreak(habitId: Long): Int {
        val habit = getHabitById(habitId) ?: return 0
        val completions = completionRepository.getCompletionsForHabit(habitId).first()
        
        if (completions.isEmpty()) return 0
        
        // Get all completion dates and sort them
        val completionDates = completions
            .filter { isHabitCompletedForCompletion(habit, it) }
            .map { LocalDate.ofEpochDay(it.timestamp / (24 * 60 * 60 * 1000L)) }
            .distinct()
            .sorted()
        
        if (completionDates.isEmpty()) return 0
        
        var maxStreak = 1
        var currentStreak = 1
        
        for (i in 1 until completionDates.size) {
            val prevDate = completionDates[i - 1]
            val currentDate = completionDates[i]
            
            // Check if dates are consecutive and both were scheduled
            if (areConsecutiveScheduledDays(habit, prevDate, currentDate)) {
                currentStreak++
                maxStreak = maxOf(maxStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }
        
        return maxStreak
    }

    /**
     * Get completion rate as percentage.
     * Formula: (Total Completions / Total Scheduled Days) * 100
     */
    suspend fun getCompletionRate(habitId: Long): Float {
        val habit = getHabitById(habitId) ?: return 0f
        val completions = completionRepository.getCompletionsForHabit(habitId).first()
        
        val creationDate = LocalDate.ofEpochDay(habit.creationDate / (24 * 60 * 60 * 1000L))
        val today = LocalDate.now()
        
        var totalScheduledDays = 0
        var totalCompletions = 0
        var currentDate = creationDate
        
        while (!currentDate.isAfter(today)) {
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)
            
            if (isScheduled) {
                totalScheduledDays++
                
                val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
                
                if (isHabitCompletedOnDay(habit, completions, dayStart)) {
                    totalCompletions++
                }
            }
            
            currentDate = currentDate.plusDays(1)
        }
        
        return if (totalScheduledDays > 0) {
            (totalCompletions.toFloat() / totalScheduledDays) * 100f
        } else {
            0f
        }
    }

    /**
     * Get total number of completions.
     */
    suspend fun getTotalCompletions(habitId: Long): Int {
        val habit = getHabitById(habitId) ?: return 0
        val completions = completionRepository.getCompletionsForHabit(habitId).first()
        
        return completions.count { isHabitCompletedForCompletion(habit, it) }
    }

    /**
     * Get total amount for measurable habits.
     */
    suspend fun getTotalAmount(habitId: Long): Double {
        val habit = getHabitById(habitId) ?: return 0.0
        if (habit.habitType != HabitType.NUMERICAL) return 0.0
        
        val completions = completionRepository.getCompletionsForHabit(habitId).first()
        
        return completions.sumOf { completion ->
            completion.value?.toDoubleOrNull() ?: 0.0
        }
    }

    /**
     * Get average value per completion for measurable habits.
     */
    suspend fun getAveragePerCompletion(habitId: Long): Double {
        val totalAmount = getTotalAmount(habitId)
        val totalCompletions = getTotalCompletions(habitId)
        
        return if (totalCompletions > 0) {
            totalAmount / totalCompletions
        } else {
            0.0
        }
    }

    /**
     * Get best day (highest value) for measurable habits.
     */
    suspend fun getBestDay(habitId: Long): Double {
        val habit = getHabitById(habitId) ?: return 0.0
        if (habit.habitType != HabitType.NUMERICAL) return 0.0

        val completions = completionRepository.getCompletionsForHabit(habitId).first()

        return completions.mapNotNull { completion ->
            completion.value?.toDoubleOrNull()
        }.maxOrNull() ?: 0.0
    }

    /**
     * Get general information for display.
     */
    fun getGeneralInfo(): GeneralInfo {
        val today = LocalDate.now()
        val weekFields = WeekFields.of(Locale.getDefault())
        val weekNumber = today.get(weekFields.weekOfYear())
        val currentWeek = "W$weekNumber"

        val dateFormatter = DateTimeFormatter.ofPattern("EEE, dd MMM", Locale.getDefault())
        val currentDate = today.format(dateFormatter)

        return GeneralInfo(
            currentWeek = currentWeek,
            currentDate = currentDate
        )
    }

    /**
     * Helper function to get habit by ID.
     */
    private suspend fun getHabitById(habitId: Long): Habit? {
        return habitRepository.getAllHabits().first().find { it.id == habitId }
    }

    /**
     * Helper function to check if habit was completed on a specific day.
     */
    private fun isHabitCompletedOnDay(habit: Habit, completions: List<Completion>, dayStart: Long): Boolean {
        val completion = completions.find { it.timestamp == dayStart }
        
        return if (habit.habitType == HabitType.NUMERICAL) {
            completion?.value?.let { valueString ->
                val value = valueString.toDoubleOrNull() ?: 0.0
                when (habit.numericalHabitType) {
                    NumericalHabitType.AT_LEAST -> value >= habit.targetValue
                    NumericalHabitType.AT_MOST -> value <= habit.targetValue
                }
            } ?: false
        } else {
            completion != null
        }
    }

    /**
     * Helper function to check if a completion meets the habit's criteria.
     */
    private fun isHabitCompletedForCompletion(habit: Habit, completion: Completion): Boolean {
        return if (habit.habitType == HabitType.NUMERICAL) {
            completion.value?.let { valueString ->
                val value = valueString.toDoubleOrNull() ?: 0.0
                when (habit.numericalHabitType) {
                    NumericalHabitType.AT_LEAST -> value >= habit.targetValue
                    NumericalHabitType.AT_MOST -> value <= habit.targetValue
                }
            } ?: false
        } else {
            true // For Yes/No habits, any completion record means it was completed
        }
    }

    /**
     * Get score history data for score charts.
     * Returns score data points aggregated by the specified time period.
     */
    suspend fun getScoreHistory(habitId: Long, timePeriod: TimePeriod): List<ScoreDataPoint> {
        val habit = getHabitById(habitId) ?: return emptyList()
        val completions = completionRepository.getCompletionsForHabit(habitId).first()

        val today = LocalDate.now()
        val startDate = when (timePeriod) {
            TimePeriod.WEEK -> today.minusWeeks(12) // Last 12 weeks
            TimePeriod.MONTH -> today.minusMonths(12) // Last 12 months
            TimePeriod.QUARTER -> today.minusMonths(24) // Last 8 quarters (24 months)
            TimePeriod.YEAR -> today.minusYears(5) // Last 5 years
        }

        val scorePoints = mutableListOf<ScoreDataPoint>()
        var currentDate = startDate

        while (!currentDate.isAfter(today)) {
            val periodEnd = when (timePeriod) {
                TimePeriod.WEEK -> currentDate.plusWeeks(1).minusDays(1)
                TimePeriod.MONTH -> currentDate.plusMonths(1).minusDays(1)
                TimePeriod.QUARTER -> currentDate.plusMonths(3).minusDays(1)
                TimePeriod.YEAR -> currentDate.plusYears(1).minusDays(1)
            }

            val score = calculatePeriodScore(habit, completions, currentDate, periodEnd)
            val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()

            scorePoints.add(ScoreDataPoint(timestamp, score))

            currentDate = when (timePeriod) {
                TimePeriod.WEEK -> currentDate.plusWeeks(1)
                TimePeriod.MONTH -> currentDate.plusMonths(1)
                TimePeriod.QUARTER -> currentDate.plusMonths(3)
                TimePeriod.YEAR -> currentDate.plusYears(1)
            }
        }

        return scorePoints
    }

    /**
     * Get completion history data for charts.
     * Returns data points aggregated by the specified time period.
     */
    suspend fun getCompletionHistory(habitId: Long, timePeriod: TimePeriod): List<ChartDataPoint> {
        val habit = getHabitById(habitId) ?: return emptyList()
        val completions = completionRepository.getCompletionsForHabit(habitId).first()

        val today = LocalDate.now()
        val startDate = when (timePeriod) {
            TimePeriod.WEEK -> today.minusWeeks(12) // Last 12 weeks
            TimePeriod.MONTH -> today.minusMonths(12) // Last 12 months
            TimePeriod.QUARTER -> today.minusMonths(24) // Last 8 quarters (24 months)
            TimePeriod.YEAR -> today.minusYears(5) // Last 5 years
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        var currentDate = startDate

        while (!currentDate.isAfter(today)) {
            val periodEnd = when (timePeriod) {
                TimePeriod.WEEK -> currentDate.plusWeeks(1).minusDays(1)
                TimePeriod.MONTH -> currentDate.plusMonths(1).minusDays(1)
                TimePeriod.QUARTER -> currentDate.plusMonths(3).minusDays(1)
                TimePeriod.YEAR -> currentDate.plusYears(1).minusDays(1)
            }

            val label = formatPeriodLabel(currentDate, timePeriod)
            val value = calculatePeriodValue(habit, completions, currentDate, periodEnd)

            dataPoints.add(ChartDataPoint(label, value))

            currentDate = when (timePeriod) {
                TimePeriod.WEEK -> currentDate.plusWeeks(1)
                TimePeriod.MONTH -> currentDate.plusMonths(1)
                TimePeriod.QUARTER -> currentDate.plusMonths(3)
                TimePeriod.YEAR -> currentDate.plusYears(1)
            }
        }

        return dataPoints
    }

    /**
     * Get calendar data for heatmap (last 6 months).
     */
    suspend fun getCalendarData(habitId: Long): Map<LocalDate, Any> {
        val habit = getHabitById(habitId) ?: return emptyMap()
        val completions = completionRepository.getCompletionsForHabit(habitId).first()

        val today = LocalDate.now()
        val startDate = today.minusMonths(6)
        val calendarData = mutableMapOf<LocalDate, Any>()

        var currentDate = startDate
        while (!currentDate.isAfter(today)) {
            val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)

            val completion = completions.find { it.timestamp == dayStart }

            if (habit.habitType == HabitType.NUMERICAL) {
                val value = completion?.value?.toDoubleOrNull()?.toFloat() ?: 0f
                calendarData[currentDate] = value
            } else {
                val isCompleted = completion != null
                calendarData[currentDate] = isCompleted
            }

            currentDate = currentDate.plusDays(1)
        }

        return calendarData
    }

    /**
     * Helper function to format period labels for charts.
     */
    private fun formatPeriodLabel(date: LocalDate, timePeriod: TimePeriod): String {
        return when (timePeriod) {
            TimePeriod.WEEK -> {
                val weekFields = WeekFields.of(Locale.getDefault())
                val weekNumber = date.get(weekFields.weekOfYear())
                "W$weekNumber"
            }
            TimePeriod.MONTH -> date.format(DateTimeFormatter.ofPattern("MMM yyyy"))
            TimePeriod.QUARTER -> {
                val quarter = (date.monthValue - 1) / 3 + 1
                "Q$quarter ${date.year}"
            }
            TimePeriod.YEAR -> date.year.toString()
        }
    }

    /**
     * Helper function to calculate value for a time period.
     */
    private fun calculatePeriodValue(
        habit: Habit,
        completions: List<Completion>,
        periodStart: LocalDate,
        periodEnd: LocalDate
    ): Float {
        val periodCompletions = completions.filter { completion ->
            val completionDate = LocalDate.ofEpochDay(completion.timestamp / (24 * 60 * 60 * 1000L))
            !completionDate.isBefore(periodStart) && !completionDate.isAfter(periodEnd) &&
                    isHabitCompletedForCompletion(habit, completion)
        }

        return if (habit.habitType == HabitType.NUMERICAL) {
            // Sum of values for measurable habits
            periodCompletions.sumOf { completion ->
                completion.value?.toDoubleOrNull() ?: 0.0
            }.toFloat()
        } else {
            // Count of completions for Yes/No habits
            periodCompletions.size.toFloat()
        }
    }

    /**
     * Helper function to calculate score for a time period.
     * Score is calculated as completion rate (0.0 to 1.0) for the period.
     */
    private fun calculatePeriodScore(
        habit: Habit,
        completions: List<Completion>,
        periodStart: LocalDate,
        periodEnd: LocalDate
    ): Double {
        var totalScheduledDays = 0
        var totalCompletions = 0
        var currentDate = periodStart

        while (!currentDate.isAfter(periodEnd)) {
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)

            if (isScheduled) {
                totalScheduledDays++

                val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)

                if (isHabitCompletedOnDay(habit, completions, dayStart)) {
                    totalCompletions++
                }
            }

            currentDate = currentDate.plusDays(1)
        }

        return if (totalScheduledDays > 0) {
            totalCompletions.toDouble() / totalScheduledDays
        } else {
            0.0
        }
    }

    /**
     * Helper function to check if two dates are consecutive scheduled days.
     */
    private fun areConsecutiveScheduledDays(habit: Habit, date1: LocalDate, date2: LocalDate): Boolean {
        var currentDate = date1.plusDays(1)

        while (currentDate.isBefore(date2)) {
            if (HabitScheduler.isHabitScheduled(habit, currentDate)) {
                return false // There's a scheduled day in between that wasn't completed
            }
            currentDate = currentDate.plusDays(1)
        }

        return currentDate == date2 && HabitScheduler.isHabitScheduled(habit, date2)
    }
}
