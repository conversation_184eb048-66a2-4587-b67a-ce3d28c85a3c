# Habit Tracker Design System
*Version 2.0 - Minimalist Redesign*

## Overview
This style guide defines the complete visual design system for the habit tracker application, focusing on minimalist aesthetics, improved spatial efficiency, and enhanced user experience across light and dark themes.

---

## Color System

### Light Theme Palette
| Token Name | Hex Code | RGB | Usage | Contrast Ratio |
|------------|----------|-----|-------|----------------|
| `background` | `#FFFFFF` | `255, 255, 255` | Base application surface | N/A |
| `text-primary` | `#2D3748` | `45, 55, 72` | Habit names, section headers, primary content | 12.6:1 |
| `text-secondary` | `#718096` | `113, 128, 150` | Dates, percentages, metadata | 4.8:1 |
| `accent-primary` | `#38B2AC` | `56, 178, 172` | Completion indicators, active states | 3.2:1 |
| `divider` | `#E2E8F0` | `226, 232, 240` | Section separators, subtle borders | 1.2:1 |
| `surface-variant` | `#F7FAFC` | `247, 250, 252` | Section backgrounds, cards | 1.05:1 |

### Dark Theme Palette
| Token Name | Hex Code | RGB | Usage | Contrast Ratio |
|------------|----------|-----|-------|----------------|
| `background` | `#121826` | `18, 24, 38` | Base application surface | N/A |
| `text-primary` | `#E2E8F0` | `226, 232, 240` | Habit names, section headers, primary content | 11.8:1 |
| `text-secondary` | `#A0AEC0` | `160, 174, 192` | Dates, percentages, metadata | 5.2:1 |
| `accent-primary` | `#81E6D9` | `129, 230, 217` | Completion indicators, active states | 4.1:1 |
| `divider` | `#2D3748` | `45, 55, 72` | Section separators, subtle borders | 2.1:1 |
| `surface-variant` | `#1A202C` | `26, 32, 44` | Section backgrounds, cards | 1.3:1 |

### Semantic Color Usage
```css
/* Light Theme Implementation */
.light-theme {
  --color-bg-primary: #FFFFFF;
  --color-text-primary: #2D3748;
  --color-text-secondary: #718096;
  --color-accent: #38B2AC;
  --color-divider: #E2E8F0;
  --color-surface: #F7FAFC;
}

/* Dark Theme Implementation */
.dark-theme {
  --color-bg-primary: #121826;
  --color-text-primary: #E2E8F0;
  --color-text-secondary: #A0AEC0;
  --color-accent: #81E6D9;
  --color-divider: #2D3748;
  --color-surface: #1A202C;
}
```

---

## Typography System

### Font Families
- **Primary:** Inter (Regular, Medium weights)
- **Secondary:** Roboto Mono (Regular weight)
- **Fallbacks:** Inter → -apple-system → BlinkMacSystemFont → "Segoe UI" → Roboto → sans-serif

### Type Scale
| Token | Font Family | Weight | Size | Line Height | Letter Spacing | Usage |
|-------|-------------|--------|------|-------------|----------------|-------|
| `display-small` | Inter | 600 (Medium) | 14px | 1.2 (17px) | -0.01em | Section headers, primary titles |
| `body-medium` | Inter | 400 (Regular) | 12px | 1.25 (15px) | 0em | Habit names, body text |
| `label-small` | Roboto Mono | 400 (Regular) | 10px | 1.3 (13px) | 0.05em | Dates, percentages, numerical data |

### Typography Classes
```css
.text-display-small {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-body-medium {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.25;
  letter-spacing: 0em;
}

.text-label-small {
  font-family: 'Roboto Mono', monospace;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.3;
  letter-spacing: 0.05em;
}
```

---

## Spacing System

### Base Units
- **Base Unit:** 4px
- **Scale:** 2px, 4px, 8px, 12px, 16px, 20px, 24px, 32px

### Spacing Tokens
| Token | Value | Usage |
|-------|-------|-------|
| `space-xxs` | 2px | Fine details, borders |
| `space-xs` | 4px | Cell spacing, tight layouts |
| `space-sm` | 8px | Compact padding, button internals |
| `space-md` | 12px | Standard component padding |
| `space-lg` | 16px | Section spacing, comfortable layouts |
| `space-xl` | 20px | Large component separation |
| `space-xxl` | 24px | Major layout divisions |
| `space-xxxl` | 32px | Page-level spacing |

### Layout Constants
```css
:root {
  /* Compact Design System */
  --spacing-cell: 4px;           /* Between interactive elements */
  --spacing-compact: 8px;        /* Reduced from previous 12px */
  --spacing-section: 16px;       /* Between major sections */
  --spacing-viewport: 20px;      /* Screen edge margins */
  
  /* Component-Specific */
  --padding-button: 8px 12px;
  --padding-card: 12px;
  --padding-section: 16px 20px;
}
```

---

## Component Specifications

### Completion Indicators
- **Size:** 18px diameter circles
- **Active State:** Filled circle (●) using `accent-primary` color
- **Inactive State:** Outlined circle (○) using `text-secondary` color
- **Touch Target:** Minimum 44px for accessibility
- **Animation:** 300ms ease-in-out color transition

### Section Headers
- **Typography:** `display-small` class
- **Layout:** Title + percentage on same line
- **Spacing:** 16px vertical margin
- **Collapse Indicator:** Chevron icon, 12px size
- **Background:** `surface-variant` color with 12px border radius

### Day Labels
- **Format:** Three-letter abbreviations (MON, TUE, WED, THU, FRI, SAT, SUN)
- **Typography:** `label-small` class
- **Orientation:** Vertical layout for space efficiency
- **Spacing:** 4px between labels
- **Alignment:** Center-aligned above completion grid

### Dividers
- **Thickness:** 1px
- **Style:** Subtle gradient from transparent to `divider` color
- **Length:** Full container width minus 20px margin
- **Spacing:** 16px above and below

---

## Layout System

### Grid Specifications
- **Completion Grid:** 7-column layout (days of week)
- **Cell Spacing:** 4px horizontal and vertical gaps
- **Row Height:** 24px (includes 18px indicator + 6px spacing)
- **Maximum Visible Rows:** 12 habits without scrolling on standard mobile

### Container Widths
- **Mobile:** 100% width with 20px side margins
- **Tablet:** Maximum 600px centered
- **Desktop:** Maximum 800px centered

### Section Layout
```css
.section-container {
  background: var(--color-surface);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0 8px 0;
}

.habit-grid {
  display: grid;
  grid-template-columns: 1fr repeat(7, 18px);
  gap: 4px;
  align-items: center;
}
```

---

## Iconography

### Icon Specifications
- **Style:** Line icons with 1.5px stroke weight
- **Size:** 16px standard, 18px for completion indicators
- **Color:** Inherits from parent text color
- **Library:** Custom icon set or Lucide icons with consistent stroke weight

### Icon Usage
| Icon | Unicode/Name | Size | Usage |
|------|-------------|------|-------|
| Completed | ● (U+25CF) | 18px | Filled completion state |
| Pending | ○ (U+25CB) | 18px | Empty completion state |
| Edit | ✏ (U+270F) | 16px | Edit habit action |
| Delete | 🗑 (U+1F5D1) | 16px | Delete habit action |
| Menu | ⋮ (U+22EE) | 16px | Overflow menu trigger |
| Expand | ▴ (U+25B4) | 12px | Section expanded state |
| Collapse | ▾ (U+25BE) | 12px | Section collapsed state |

---

## Animation Standards

### Transition Specifications
| Property | Duration | Easing | Usage |
|----------|----------|--------|-------|
| Color changes | 300ms | ease-in-out | Theme switching, state changes |
| Height/Width | 250ms | ease-out | Section expansion, layout changes |
| Opacity | 200ms | ease-in-out | Content fade in/out |
| Transform | 150ms | ease-out | Button press feedback |

### Micro-Interactions
```css
/* Completion Ripple Effect */
.completion-indicator {
  transition: all 300ms ease-in-out;
}

.completion-indicator:active {
  transform: scale(0.9);
  transition-duration: 150ms;
}

/* Section Expansion */
.section-content {
  transition: height 250ms ease-out, opacity 200ms ease-in-out;
}

/* Drag Feedback */
.habit-item.dragging {
  transform: scale(0.9);
  opacity: 0.8;
  transition: transform 150ms ease-out;
}
```

---

## Accessibility Guidelines

### Contrast Requirements
- **Normal Text:** Minimum 4.5:1 ratio (AA standard)
- **Large Text:** Minimum 3:1 ratio (AA standard)
- **Interactive Elements:** Minimum 3:1 ratio for focus indicators

### Touch Targets
- **Minimum Size:** 44px × 44px
- **Recommended Size:** 48px × 48px for primary actions
- **Spacing:** Minimum 8px between adjacent touch targets

### Focus States
```css
.focusable:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
  border-radius: 4px;
}

.focusable:focus:not(:focus-visible) {
  outline: none;
}
```

### Screen Reader Support
- All interactive elements must have appropriate ARIA labels
- Section headers use proper heading hierarchy (h2, h3)
- Completion state changes announce to screen readers
- Loading states include `aria-live` regions

---

## Responsive Breakpoints

### Breakpoint System
```css
/* Mobile First Approach */
@media (min-width: 480px) { /* Large mobile */ }
@media (min-width: 768px) { /* Tablet */ }
@media (min-width: 1024px) { /* Desktop */ }
```

### Layout Adaptations
- **Mobile (< 480px):** Single column, full-width sections
- **Tablet (480px - 768px):** Larger touch targets, increased spacing
- **Desktop (> 768px):** Maximum width constraints, hover states

---

## Performance Considerations

### Optimization Guidelines
- Use CSS transforms for animations (hardware accelerated)
- Minimize repaints during scroll and interaction
- Lazy load habit data beyond current week view
- Debounce rapid completion state changes (200ms)

### Bundle Size Targets
- **CSS:** < 15KB compressed
- **Fonts:** Inter + Roboto Mono subset < 40KB
- **Icons:** SVG sprite < 5KB

---

## Implementation Checklist

### Theme System
- [ ] CSS custom properties for all color tokens
- [ ] Theme switching mechanism with persistence
- [ ] Automatic system preference detection
- [ ] Theme-specific asset loading

### Typography
- [ ] Font loading with fallbacks
- [ ] Responsive font scaling
- [ ] Proper line height for readability
- [ ] Letter spacing optimization

### Layout
- [ ] Flexible grid system implementation
- [ ] Responsive breakpoint handling
- [ ] Accessibility compliance verification
- [ ] Cross-browser compatibility testing

### Interactions
- [ ] Touch feedback implementation
- [ ] Animation performance testing
- [ ] Focus management
- [ ] Keyboard navigation support

---

## Maintenance Notes

### Version Control
- Update version number for any breaking changes
- Maintain backwards compatibility for one major version
- Document all changes in style guide changelog

### Browser Support
- **Modern Browsers:** Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **Legacy Support:** Graceful degradation for older browsers
- **Mobile:** iOS 12+, Android 8+ (API level 26)

### Future Considerations
- Dark mode preference scheduling
- High contrast mode support
- Reduced motion preferences
- Custom theme creation capabilities

---

*Last Updated: [Current Date]*
*Next Review: [Quarterly Review Date]*