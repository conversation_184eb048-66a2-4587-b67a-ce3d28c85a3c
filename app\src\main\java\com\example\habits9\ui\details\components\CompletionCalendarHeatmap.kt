package com.example.habits9.ui.details.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.math.max
import kotlin.math.min

/**
 * Completion Calendar Heatmap component for habit analytics.
 * Displays completion data as a calendar heatmap with color intensity based on values.
 */
@Composable
fun CompletionCalendarHeatmap(
    calendarData: Map<LocalDate, Any>, // Boolean for Yes/No habits, Float for measurable habits
    isYesNoHabit: Boolean,
    modifier: Modifier = Modifier
) {
    // Theme colors
    val backgroundColor = MaterialTheme.colorScheme.surface
    val textColor = MaterialTheme.colorScheme.onSurface
    val accentColor = MaterialTheme.colorScheme.primary
    val secondaryTextColor = MaterialTheme.colorScheme.onSurfaceVariant
    val dividerColor = MaterialTheme.colorScheme.outline

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp) // --padding-card from style guide
        ) {
            // Header
            Text(
                text = "Completion Calendar",
                color = textColor,
                fontSize = 14.sp, // display-small from style guide
                fontFamily = FontFamily.Default, // Inter font family
                fontWeight = FontWeight.SemiBold, // 600 weight from style guide
                letterSpacing = (-0.01).sp // -0.01em from style guide
            )
            
            Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide
            
            if (calendarData.isNotEmpty()) {
                // Generate calendar grid for last 6 months
                val today = LocalDate.now()
                val sixMonthsAgo = today.minusMonths(6)
                val monthsToShow = generateMonthsToShow(sixMonthsAgo, today)
                
                // Calculate max value for measurable habits (for color scaling)
                val maxValue = if (!isYesNoHabit) {
                    calendarData.values.filterIsInstance<Float>().maxOrNull() ?: 1f
                } else 1f
                
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp) // space-lg from style guide
                ) {
                    items(monthsToShow) { monthData ->
                        MonthCalendarGrid(
                            monthData = monthData,
                            calendarData = calendarData,
                            isYesNoHabit = isYesNoHabit,
                            maxValue = maxValue,
                            accentColor = accentColor,
                            textColor = textColor,
                            secondaryTextColor = secondaryTextColor,
                            dividerColor = dividerColor
                        )
                    }
                }
            } else {
                // Empty state
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(160.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        color = secondaryTextColor,
                        fontSize = 12.sp,
                        fontFamily = FontFamily.Default
                    )
                }
            }
        }
    }
}

@Composable
private fun MonthCalendarGrid(
    monthData: MonthData,
    calendarData: Map<LocalDate, Any>,
    isYesNoHabit: Boolean,
    maxValue: Float,
    accentColor: Color,
    textColor: Color,
    secondaryTextColor: Color,
    dividerColor: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Month header
        Text(
            text = monthData.monthName,
            color = textColor,
            fontSize = 10.sp, // label-small from style guide
            fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
            fontWeight = FontWeight.Normal,
            letterSpacing = 0.05.sp, // 0.05em from style guide
            modifier = Modifier.padding(bottom = 4.dp) // space-xs from style guide
        )
        
        // Calendar grid (7 columns for days of week, rows for weeks)
        Column(
            verticalArrangement = Arrangement.spacedBy(2.dp) // space-xxs from style guide
        ) {
            // Week day headers
            Row(
                horizontalArrangement = Arrangement.spacedBy(2.dp) // space-xxs from style guide
            ) {
                listOf("S", "M", "T", "W", "T", "F", "S").forEach { dayName ->
                    Box(
                        modifier = Modifier.size(12.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = dayName,
                            color = secondaryTextColor,
                            fontSize = 8.sp, // Smaller than label-small for compact display
                            fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.05.sp // 0.05em from style guide
                        )
                    }
                }
            }
            
            // Calendar days
            monthData.weeks.forEach { week ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(2.dp) // space-xxs from style guide
                ) {
                    week.forEach { date ->
                        CalendarDay(
                            date = date,
                            value = calendarData[date],
                            isYesNoHabit = isYesNoHabit,
                            maxValue = maxValue,
                            accentColor = accentColor,
                            dividerColor = dividerColor
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CalendarDay(
    date: LocalDate?,
    value: Any?,
    isYesNoHabit: Boolean,
    maxValue: Float,
    accentColor: Color,
    dividerColor: Color
) {
    val dayColor = when {
        date == null -> Color.Transparent // Empty day slot
        value == null -> dividerColor.copy(alpha = 0.3f) // No data
        isYesNoHabit -> {
            // Binary coloring for Yes/No habits
            if (value as Boolean) accentColor else dividerColor.copy(alpha = 0.3f)
        }
        else -> {
            // Graded coloring for measurable habits
            val intensity = min(1f, max(0f, (value as Float) / maxValue))
            accentColor.copy(alpha = 0.2f + (intensity * 0.8f))
        }
    }
    
    Box(
        modifier = Modifier
            .size(12.dp)
            .background(
                color = dayColor,
                shape = RoundedCornerShape(2.dp)
            )
            .border(
                width = 0.5.dp,
                color = dividerColor.copy(alpha = 0.2f),
                shape = RoundedCornerShape(2.dp)
            )
    )
}

// Data classes and helper functions
private data class MonthData(
    val monthName: String,
    val weeks: List<List<LocalDate?>>
)

private fun generateMonthsToShow(startDate: LocalDate, endDate: LocalDate): List<MonthData> {
    val months = mutableListOf<MonthData>()
    var currentDate = startDate.withDayOfMonth(1)
    
    while (!currentDate.isAfter(endDate)) {
        val monthName = currentDate.format(DateTimeFormatter.ofPattern("MMM"))
        val weeks = generateWeeksForMonth(currentDate)
        months.add(MonthData(monthName, weeks))
        currentDate = currentDate.plusMonths(1)
    }
    
    return months
}

private fun generateWeeksForMonth(monthStart: LocalDate): List<List<LocalDate?>> {
    val weeks = mutableListOf<List<LocalDate?>>()
    val monthEnd = monthStart.plusMonths(1).minusDays(1)

    // Start from the first day of the month
    var currentDate = monthStart

    // Find the first Sunday of the calendar (may be in previous month)
    val firstDayOfWeek = monthStart.dayOfWeek.value % 7 // Convert to Sunday = 0
    val calendarStart = monthStart.minusDays(firstDayOfWeek.toLong())

    var weekStart = calendarStart

    // Generate weeks until we've covered the entire month
    while (weekStart <= monthEnd) {
        val week = mutableListOf<LocalDate?>()

        for (i in 0..6) {
            val dayDate = weekStart.plusDays(i.toLong())
            if (dayDate.month == monthStart.month) {
                week.add(dayDate)
            } else {
                week.add(null) // Days from other months
            }
        }

        weeks.add(week)
        weekStart = weekStart.plusWeeks(1)

        // Safety check to prevent infinite loop
        if (weeks.size > 6) break
    }

    return weeks
}
