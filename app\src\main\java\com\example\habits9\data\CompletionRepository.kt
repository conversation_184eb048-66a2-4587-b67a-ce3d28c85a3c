package com.example.habits9.data

import android.util.Log
import com.example.habits9.data.firestore.FirestoreCompletion
import com.example.habits9.data.firestore.FirestoreConverters
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing habit completion data.
 * Serves as an abstraction layer between ViewModels and Firestore.
 */
@Singleton
class CompletionRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {

    companion object {
        private const val TAG = "CompletionRepository"
        private const val USERS_COLLECTION = "users"
        private const val COMPLETIONS_COLLECTION = "completions"
    }

    // Cache to store habitId -> Firestore document ID mapping
    private val habitIdToDocumentIdCache = mutableMapOf<Long, String>()

    /**
     * FIXED: Helper method to find the Firestore document ID for a given habit Long ID
     * This is needed because we convert Firestore document IDs to Long IDs using hash codes
     */
    private suspend fun findHabitDocumentId(habitId: Long): String {
        // Check cache first
        habitIdToDocumentIdCache[habitId]?.let { return it }

        val userId = auth.currentUser?.uid ?: throw IllegalStateException("User not authenticated")

        try {
            // Query all habits to find the one with matching hash-based ID
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection("habits")
                .get()
                .await()

            for (document in snapshot.documents) {
                val documentId = document.id
                val hashBasedId = documentId.hashCode().toLong().let { if (it == 0L) 1L else kotlin.math.abs(it) }
                if (hashBasedId == habitId) {
                    // Cache the result
                    habitIdToDocumentIdCache[habitId] = documentId
                    return documentId
                }
            }

            // Fallback: use the habitId as string (for backward compatibility)
            Log.w(TAG, "Could not find Firestore document ID for habit $habitId, using habitId as string")
            return habitId.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Error finding habit document ID for $habitId", e)
            return habitId.toString()
        }
    }

    /**
     * Inserts a new completion record for the current user.
     */
    suspend fun insertCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot insert completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // FIXED: We need to find the actual Firestore document ID for this habit
            // Since we're using hash-based IDs, we need to reverse-lookup the Firestore document ID
            val habitDocumentId = findHabitDocumentId(completion.habitId)

            val firestoreCompletion = FirestoreConverters.completionToFirestore(
                completion,
                habitDocumentId // Use actual Firestore document ID
            )

            Log.d(TAG, "Inserting completion - HabitId: ${completion.habitId}, Timestamp: ${completion.timestamp}, Value: ${completion.value}")
            Log.d(TAG, "Firestore completion - HabitId: ${firestoreCompletion.habitId}, Timestamp: ${firestoreCompletion.timestamp}")

            // Add the completion to Firestore (auto-generate document ID)
            val documentRef = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .add(firestoreCompletion)
                .await()

            Log.d(TAG, "Completion inserted with ID: ${documentRef.id} for habit ${completion.habitId}")
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting completion for habit ${completion.habitId}", e)
            throw e
        }
    }

    /**
     * Deletes a completion record for the current user.
     */
    suspend fun deleteCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            Log.d(TAG, "Deleting completion - ID: ${completion.id}, HabitId: ${completion.habitId}, Timestamp: ${completion.timestamp}")

            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .document(completion.id) // Use the Firestore document ID directly
                .delete()
                .await()

            Log.d(TAG, "Completion deleted: ${completion.id} for habit ${completion.habitId}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting completion ${completion.id} for habit ${completion.habitId}", e)
            throw e
        }
    }

    /**
     * Updates an existing completion record for the current user.
     */
    suspend fun updateCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // FIXED: Use actual Firestore document ID for consistency
            val habitDocumentId = findHabitDocumentId(completion.habitId)

            val firestoreCompletion = FirestoreConverters.completionToFirestore(
                completion,
                habitDocumentId
            )

            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .document(completion.id) // Use the Firestore document ID directly
                .set(firestoreCompletion)
                .await()

            Log.d(TAG, "Completion updated: ${completion.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating completion ${completion.id}", e)
            throw e
        }
    }

    /**
     * Gets all completions for a list of habit IDs within a specific date range.
     * Returns a Flow for reactive updates using real-time listeners.
     *
     * Note: This method uses separate queries for each habit to avoid Firestore composite index requirements.
     * Firestore requires a composite index for queries combining whereIn() with range queries.
     */
    fun getCompletionsForHabitsInRange(
        habitIds: List<Long>,
        startDate: Long,
        endDate: Long
    ): Flow<List<Completion>> = callbackFlow {
        val userId = auth.currentUser?.uid
        val listeners = mutableListOf<ListenerRegistration>()

        // Handle authentication and empty habitIds gracefully without early return
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty completions list")
            trySend(emptyList())
        } else if (habitIds.isEmpty()) {
            trySend(emptyList())
        } else {
            val completionsMap = mutableMapOf<String, List<Completion>>()

            // FIXED: Create separate listeners for each habit using actual Firestore document IDs
            // We need to handle this asynchronously within the callbackFlow
            launch {
                try {
                    for (habitId in habitIds) {
                        val habitDocumentId = findHabitDocumentId(habitId)
                        val listener = firestore
                            .collection(USERS_COLLECTION)
                            .document(userId)
                            .collection(COMPLETIONS_COLLECTION)
                            .whereEqualTo("habitId", habitDocumentId)
                            .whereGreaterThanOrEqualTo("timestamp", startDate)
                            .whereLessThanOrEqualTo("timestamp", endDate)
                            .addSnapshotListener { snapshot, error ->
                                if (error != null) {
                                    Log.e(TAG, "Error listening to completions for habit $habitId", error)
                                    close(error)
                                    return@addSnapshotListener
                                }

                                if (snapshot != null) {
                                    val completions = snapshot.documents.mapNotNull { document ->
                                        try {
                                            val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                                            firestoreCompletion?.let {
                                                val completionWithId = it.copy(id = document.id)
                                                FirestoreConverters.firestoreToCompletion(completionWithId, habitId)
                                            }
                                        } catch (e: Exception) {
                                            Log.e(TAG, "Error converting document to completion: ${document.id}", e)
                                            null
                                        }
                                    }

                                    // Update the completions for this habit
                                    completionsMap[habitId.toString()] = completions

                                    // Combine all completions and send the updated list
                                    val allCompletions = completionsMap.values.flatten()
                                    trySend(allCompletions)
                                } else {
                                    // Handle empty snapshot for this habit
                                    completionsMap[habitId.toString()] = emptyList()
                                    val allCompletions = completionsMap.values.flatten()
                                    trySend(allCompletions)
                                }
                            }

                        listeners.add(listener)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting up completion listeners", e)
                    close(e)
                }
            }
        }

        // FIXED: awaitClose must be at the end of callbackFlow block for all execution paths
        awaitClose {
            listeners.forEach { it.remove() }
        }
    }

    /**
     * Gets all completions for a specific habit using real-time listeners.
     */
    fun getCompletionsForHabit(habitId: Long): Flow<List<Completion>> = callbackFlow {
        val userId = auth.currentUser?.uid
        var listener: ListenerRegistration? = null

        // Handle authentication gracefully without early return
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty completions list")
            trySend(emptyList())
        } else {
            // FIXED: Use actual Firestore document ID for querying
            launch {
                try {
                    val habitDocumentId = findHabitDocumentId(habitId)
                    listener = firestore
                        .collection(USERS_COLLECTION)
                        .document(userId)
                        .collection(COMPLETIONS_COLLECTION)
                        .whereEqualTo("habitId", habitDocumentId)
                        .addSnapshotListener { snapshot, error ->
                            if (error != null) {
                                Log.e(TAG, "Error listening to completions for habit $habitId", error)
                                close(error)
                                return@addSnapshotListener
                            }

                            if (snapshot != null) {
                                val completions = snapshot.documents.mapNotNull { document ->
                                    try {
                                        val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                                        firestoreCompletion?.let {
                                            val completionWithId = it.copy(id = document.id)
                                            FirestoreConverters.firestoreToCompletion(completionWithId, habitId)
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "Error converting document to completion: ${document.id}", e)
                                        null
                                    }
                                }
                                trySend(completions)
                            } else {
                                trySend(emptyList())
                            }
                        }
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting up completion listener for habit $habitId", e)
                    close(e)
                }
            }
        }

        // FIXED: awaitClose must be at the end of callbackFlow block for all execution paths
        awaitClose { listener?.remove() }
    }

    /**
     * Gets a specific completion for a habit on a specific date.
     * Returns null if no completion exists.
     */
    suspend fun getCompletionForHabitAndDate(habitId: Long, timestamp: Long): Completion? {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            return null
        }

        return try {
            // FIXED: Use actual Firestore document ID for querying
            val habitDocumentId = findHabitDocumentId(habitId)
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .whereEqualTo("habitId", habitDocumentId)
                .whereEqualTo("timestamp", timestamp)
                .get()
                .await()

            if (!snapshot.isEmpty) {
                val document = snapshot.documents.first()
                val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                firestoreCompletion?.let {
                    val completionWithId = it.copy(id = document.id)
                    FirestoreConverters.firestoreToCompletion(completionWithId, habitId)
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting completion for habit $habitId at $timestamp", e)
            null
        }
    }

    /**
     * Deletes all completions for a specific habit.
     */
    suspend fun deleteAllCompletionsForHabit(habitId: Long) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete completions")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // FIXED: Use actual Firestore document ID for querying
            val habitDocumentId = findHabitDocumentId(habitId)
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .whereEqualTo("habitId", habitDocumentId)
                .get()
                .await()

            // Delete all matching documents
            val batch = firestore.batch()
            snapshot.documents.forEach { document ->
                batch.delete(document.reference)
            }
            batch.commit().await()

            Log.d(TAG, "All completions deleted for habit: $habitId")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting completions for habit $habitId", e)
            throw e
        }
    }
}