package com.example.habits9.data.analytics

import java.time.LocalDate

/**
 * Data class representing analytics for Yes/No habits.
 * Contains all calculated metrics for display in the UI.
 */
data class YesNoHabitAnalytics(
    val currentStreak: Int,
    val longestStreak: Int,
    val completionRate: Float, // Percentage (0-100)
    val totalCompletions: Int,
    val completionHistory: List<ChartDataPoint>,
    val scoreHistory: List<ScoreDataPoint>,
    val calendarData: Map<LocalDate, Boolean>
)

/**
 * Data class representing analytics for measurable habits.
 * Contains all calculated metrics including numerical values.
 */
data class MeasurableHabitAnalytics(
    val currentStreak: Int,
    val longestStreak: Int,
    val completionRate: Float, // Percentage (0-100)
    val totalCompletions: Int,
    val totalAmount: Double,
    val averagePerCompletion: Double,
    val bestDay: Double,
    val completionHistory: List<ChartDataPoint>,
    val scoreHistory: List<ScoreDataPoint>,
    val calendarData: Map<LocalDate, Float>
)

/**
 * Data class representing a single data point for chart visualization.
 */
data class ChartDataPoint(
    val label: String, // Time period label (e.g., "Week 1", "Jan 2024")
    val value: Float   // Count for Yes/No habits, sum for measurable habits
)

/**
 * Data class representing a single score data point for score chart visualization.
 */
data class ScoreDataPoint(
    val timestamp: Long, // Unix timestamp
    val score: Double    // Score value (0.0 to 1.0 representing 0% to 100%)
)

/**
 * Enum representing different time periods for chart data aggregation.
 */
enum class TimePeriod {
    WEEK,
    MONTH,
    QUARTER,
    YEAR
}

/**
 * Data class for general information displayed in analytics.
 */
data class GeneralInfo(
    val currentWeek: String,  // Format: "W34"
    val currentDate: String   // Format: "Sat, 09 Aug"
)
