# Habit Tracker Design System

_Version 3.0 - Enhanced Modern Implementation_

## Overview

This style guide defines the complete visual design system for the habit tracker application, focusing on minimalist aesthetics, improved spatial efficiency, and enhanced user experience across light and dark themes. This version reflects the current implementation using Flutter with Material 3 design principles, Google Fonts (Inter & Roboto Mono), and a comprehensive component library.

---

## Color System

### Light Theme Palette

| Token Name        | Hex Code  | RGB             | Usage                                         | Contrast Ratio |
| ----------------- | --------- | --------------- | --------------------------------------------- | -------------- |
| `background`      | `#FFFFFF` | `255, 255, 255` | Base application surface                      | N/A            |
| `text-primary`    | `#2D3748` | `45, 55, 72`    | Habit names, section headers, primary content | 12.6:1         |
| `text-secondary`  | `#718096` | `113, 128, 150` | Dates, percentages, metadata                  | 4.8:1          |
| `accent-primary`  | `#38B2AC` | `56, 178, 172`  | Completion indicators, active states          | 3.2:1          |
| `divider`         | `#E2E8F0` | `226, 232, 240` | Section separators, subtle borders            | 1.2:1          |
| `surface-variant` | `#F7FAFC` | `247, 250, 252` | Section backgrounds, cards                    | 1.05:1         |

### Dark Theme Palette

| Token Name        | Hex Code  | RGB             | Usage                                         | Contrast Ratio |
| ----------------- | --------- | --------------- | --------------------------------------------- | -------------- |
| `background`      | `#121826` | `18, 24, 38`    | Base application surface                      | N/A            |
| `text-primary`    | `#E2E8F0` | `226, 232, 240` | Habit names, section headers, primary content | 11.8:1         |
| `text-secondary`  | `#A0AEC0` | `160, 174, 192` | Dates, percentages, metadata                  | 5.2:1          |
| `accent-primary`  | `#81E6D9` | `129, 230, 217` | Completion indicators, active states          | 4.1:1          |
| `divider`         | `#2D3748` | `45, 55, 72`    | Section separators, subtle borders            | 2.1:1          |
| `surface-variant` | `#1A202C` | `26, 32, 44`    | Section backgrounds, cards                    | 1.3:1          |

### Semantic Color Usage

```css
/* Light Theme Implementation */
.light-theme {
  --color-bg-primary: #ffffff;
  --color-text-primary: #2d3748;
  --color-text-secondary: #718096;
  --color-accent: #38b2ac;
  --color-divider: #e2e8f0;
  --color-surface: #f7fafc;
}

/* Dark Theme Implementation */
.dark-theme {
  --color-bg-primary: #121826;
  --color-text-primary: #e2e8f0;
  --color-text-secondary: #a0aec0;
  --color-accent: #81e6d9;
  --color-divider: #2d3748;
  --color-surface: #1a202c;
}
```

---

## Typography System

### Font Families

- **Primary:** Inter (Regular 400, Medium 500, SemiBold 600, Bold 700 weights)
- **Secondary:** Roboto Mono (Regular 400 weight)
- **Implementation:** Google Fonts package (google_fonts: ^6.2.1)
- **Fallbacks:** Inter → -apple-system → BlinkMacSystemFont → "Segoe UI" → Roboto → sans-serif

### Type Scale

| Token           | Font Family | Weight        | Size | Line Height | Letter Spacing | Usage                              |
| --------------- | ----------- | ------------- | ---- | ----------- | -------------- | ---------------------------------- |
| `display-small` | Inter       | 600 (Medium)  | 14px | 1.2 (17px)  | -0.01em        | Section headers, primary titles    |
| `body-medium`   | Inter       | 400 (Regular) | 12px | 1.25 (15px) | 0em            | Habit names, body text             |
| `label-small`   | Roboto Mono | 400 (Regular) | 10px | 1.3 (13px)  | 0.05em         | Dates, percentages, numerical data |

### Typography Classes

```css
.text-display-small {
  font-family: "Inter", sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-body-medium {
  font-family: "Inter", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.25;
  letter-spacing: 0em;
}

.text-label-small {
  font-family: "Roboto Mono", monospace;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.3;
  letter-spacing: 0.05em;
}
```

---

## Spacing System

### Base Units

- **Base Unit:** 4px
- **Scale:** 2px, 4px, 8px, 12px, 16px, 20px, 24px, 32px

### Spacing Tokens

| Token        | Value | Usage                                |
| ------------ | ----- | ------------------------------------ |
| `space-xxs`  | 2px   | Fine details, borders                |
| `space-xs`   | 4px   | Cell spacing, tight layouts          |
| `space-sm`   | 8px   | Compact padding, button internals    |
| `space-md`   | 12px  | Standard component padding           |
| `space-lg`   | 16px  | Section spacing, comfortable layouts |
| `space-xl`   | 20px  | Large component separation           |
| `space-xxl`  | 24px  | Major layout divisions               |
| `space-xxxl` | 32px  | Page-level spacing                   |

### Layout Constants

```css
:root {
  /* Compact Design System */
  --spacing-cell: 4px; /* Between interactive elements */
  --spacing-compact: 8px; /* Reduced from previous 12px */
  --spacing-section: 16px; /* Between major sections */
  --spacing-viewport: 20px; /* Screen edge margins */

  /* Component-Specific */
  --padding-button: 8px 12px;
  --padding-card: 12px;
  --padding-section: 16px 20px;
}
```

---

## Component Specifications

### Completion Indicators

- **Size:** 18px diameter circles (21.6px in dark mode - 20% larger)
- **Active State:** Filled circle with checkmark icon using `accent-primary` color
- **Inactive State:** Outlined circle using `text-secondary` color with 2px border
- **Today Indicator:** Special border styling with primary color
- **Touch Target:** 32px × 32px container with minimum 44px accessibility target
- **Animation:** 300ms ease-in-out color transition, 200ms scale animation on tap
- **Shadow:** Subtle shadow on completed state for depth

### Section Headers

- **Typography:** `display-small` class
- **Layout:** Title + percentage on same line
- **Spacing:** 16px vertical margin
- **Collapse Indicator:** Chevron icon, 12px size
- **Background:** `surface-variant` color with 12px border radius

### Day Labels

- **Format:** Three-letter abbreviations (MON, TUE, WED, THU, FRI, SAT, SUN)
- **Typography:** `label-small` class
- **Orientation:** Vertical layout for space efficiency
- **Spacing:** 4px between labels
- **Alignment:** Center-aligned above completion grid

### Dividers

- **Thickness:** 1px
- **Style:** Subtle gradient from transparent to `divider` color
- **Length:** Full container width minus 20px margin
- **Spacing:** 16px above and below

---

## Layout System

### Grid Specifications

- **Completion Grid:** 7-column layout (days of week)
- **Cell Spacing:** 4px horizontal and vertical gaps
- **Row Height:** 24px (includes 18px indicator + 6px spacing)
- **Maximum Visible Rows:** 12 habits without scrolling on standard mobile

### Container Widths

- **Mobile:** 100% width with 20px side margins
- **Tablet:** Maximum 600px centered
- **Desktop:** Maximum 800px centered

### Section Layout

```css
.section-container {
  background: var(--color-surface);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0 8px 0;
}

.habit-grid {
  display: grid;
  grid-template-columns: 1fr repeat(7, 18px);
  gap: 4px;
  align-items: center;
}
```

---

## Modern Component Library

### ModernCard Component

- **Elevation:** 2px default, customizable
- **Padding:** 12px (25% reduction from 16px for density)
- **Margin:** 8px default
- **Border Radius:** 12px
- **Tap Support:** Optional InkWell with matching border radius

### ModernHabitCompletionIndicator

- **Container:** 32px × 32px with circular shape
- **Animation:** Scale animation (1.0 → 0.9) on tap with 200ms duration
- **States:** Completed (filled + checkmark), incomplete (border only), today (special border)
- **Colors:** Dynamic based on theme with success color variants

### ModernDateChip

- **Layout:** Vertical stack (percentage, day abbreviation, day number)
- **Padding:** 8.8px horizontal, 8px vertical (optimized spacing)
- **Border Radius:** 16px
- **Selection State:** Primary color background with shadow
- **Today State:** Primary color border when not selected

### Analytics Widgets

- **CircularScoreWidget:** Custom painter with 8% stroke width relative to size
- **LinearScoreWidget:** 6px height progress bar with habit name and percentage
- **StreakDisplayWidget:** Card layout with emoji icons and color-coded streak values
- **CompactScoreStreakWidget:** Inline chips with rounded corners and opacity backgrounds

---

## Iconography

### Icon Specifications

- **Style:** Material Icons with consistent sizing
- **Size:** 16px standard, 20px for completion checkmarks, 24px for buttons
- **Color:** Inherits from parent text color or theme-specific colors
- **Library:** Material Icons (built-in Flutter icons)

### Icon Usage

| Icon       | Material Icon                    | Size | Usage                              |
| ---------- | -------------------------------- | ---- | ---------------------------------- |
| Completed  | Icons.check                      | 20px | Checkmark in completion indicators |
| Info       | Icons.info_outline               | 16px | Information indicators             |
| Menu       | Icons.drag_handle                | 16px | Reorder handles                    |
| Navigation | Icons.keyboard_arrow_down        | 20px | Dropdown indicators                |
| Analytics  | Icons.library_add_check_outlined | 60px | Empty state illustrations          |

---

## Animation Standards

### Transition Specifications

| Property      | Duration | Easing      | Usage                             |
| ------------- | -------- | ----------- | --------------------------------- |
| Color changes | 300ms    | ease-in-out | Theme switching, state changes    |
| Height/Width  | 250ms    | ease-out    | Section expansion, layout changes |
| Opacity       | 200ms    | ease-in-out | Content fade in/out               |
| Transform     | 150ms    | ease-out    | Button press feedback             |

### Micro-Interactions

```css
/* Completion Ripple Effect */
.completion-indicator {
  transition: all 300ms ease-in-out;
}

.completion-indicator:active {
  transform: scale(0.9);
  transition-duration: 150ms;
}

/* Section Expansion */
.section-content {
  transition: height 250ms ease-out, opacity 200ms ease-in-out;
}

/* Drag Feedback */
.habit-item.dragging {
  transform: scale(0.9);
  opacity: 0.8;
  transition: transform 150ms ease-out;
}
```

---

## Accessibility Guidelines

### Contrast Requirements

- **Normal Text:** Minimum 4.5:1 ratio (AA standard)
- **Large Text:** Minimum 3:1 ratio (AA standard)
- **Interactive Elements:** Minimum 3:1 ratio for focus indicators

### Touch Targets

- **Minimum Size:** 44px × 44px
- **Recommended Size:** 48px × 48px for primary actions
- **Spacing:** Minimum 8px between adjacent touch targets

### Focus States

```css
.focusable:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
  border-radius: 4px;
}

.focusable:focus:not(:focus-visible) {
  outline: none;
}
```

### Screen Reader Support

- All interactive elements must have appropriate ARIA labels
- Section headers use proper heading hierarchy (h2, h3)
- Completion state changes announce to screen readers
- Loading states include `aria-live` regions

---

## Responsive Breakpoints

### Breakpoint System

```css
/* Mobile First Approach */
@media (min-width: 480px) {
  /* Large mobile */
}
@media (min-width: 768px) {
  /* Tablet */
}
@media (min-width: 1024px) {
  /* Desktop */
}
```

### Layout Adaptations

- **Mobile (< 480px):** Single column, full-width sections
- **Tablet (480px - 768px):** Larger touch targets, increased spacing
- **Desktop (> 768px):** Maximum width constraints, hover states

## Color System Extensions

### Section Color Palette

The application includes a comprehensive color palette for section categorization:

#### Light Theme Section Colors

- Blue: `#3B82F6`, Green: `#10B981`, Purple: `#8B5CF6`, Orange: `#F97316`
- Pink: `#EC4899`, Teal: `#06B6D4`, Red: `#EF4444`, Indigo: `#6366F1`
- Yellow: `#EAB308`, Emerald: `#059669`, Rose: `#F43F5E`, Cyan: `#0891B2`

#### Dark Theme Section Colors

- Blue: `#60A5FA`, Green: `#34D399`, Purple: `#A78BFA`, Orange: `#FB923C`
- Pink: `#F472B6`, Teal: `#22D3EE`, Red: `#F87171`, Indigo: `#818CF8`
- Yellow: `#FBBF24`, Emerald: `#10B981`, Rose: `#FB7185`, Cyan: `#06B6D4`

### Smart Color Coding for Completion Percentages

- **0%:** Neutral gray (`#6B7280` dark, `#9CA3AF` light)
- **1-33%:** Red tier (`#F87171` dark, `#EF4444` light)
- **34-66%:** Amber tier (`#FBBF24` dark, `#F59E0B` light)
- **67-89%:** Blue tier (`#60A5FA` dark, `#3B82F6` light)
- **90-100%:** Green tier (`#34D399` dark, `#10B981` light)

---

## Performance Considerations

### Flutter-Specific Optimizations

- Use `AnimationController` with `SingleTickerProviderStateMixin` for smooth animations
- Implement `TableView.builder` for efficient large dataset rendering
- Utilize `TextPainter` for dynamic text measurement and layout optimization
- Debounce rapid completion state changes (200ms)
- Implement proper `dispose()` methods for controllers

### Package Dependencies

- **google_fonts:** ^6.2.1 (Inter & Roboto Mono)
- **two_dimensional_scrollables:** ^0.3.6 (TableView)
- **sembast:** ^3.7.1 (Local database)
- **provider:** ^6.0.0 (State management)

---

## Implementation Status

### Theme System ✅

- [x] Material 3 theme implementation with custom color schemes
- [x] Theme switching mechanism with persistence via SharedPreferences
- [x] Automatic system preference detection
- [x] Dynamic theme-specific color loading for sections

### Typography ✅

- [x] Google Fonts integration (Inter & Roboto Mono)
- [x] Responsive font scaling with dynamic text measurement
- [x] Optimized line height and letter spacing
- [x] Text wrapping with maxLines support

### Layout ✅

- [x] TableView.builder for efficient grid rendering
- [x] Dynamic row height calculation using TextPainter
- [x] Responsive spacing system with ModernTheme constants
- [x] Accessibility compliance with minimum touch targets

### Interactions ✅

- [x] HapticFeedback integration for tactile responses
- [x] Smooth animations with AnimationController
- [x] Gesture detection (tap, long press, drag)
- [x] Focus management and keyboard navigation

### Modern Components ✅

- [x] ModernCard with customizable elevation and padding
- [x] ModernHabitCompletionIndicator with scale animations
- [x] ModernDateChip with selection states
- [x] Analytics widgets (Circular, Linear, Streak displays)
- [x] Color selection widget with theme-aware palettes

---

## Maintenance Notes

### Version Control

- Update version number for any breaking changes
- Maintain backwards compatibility for one major version
- Document all changes in style guide changelog

### Platform Support

- **Flutter SDK:** ^3.8.1 with Material 3 support
- **Mobile:** iOS 12+, Android API level 21+ (Android 5.0)
- **Desktop:** Windows, macOS, Linux (Flutter desktop support)
- **Web:** Modern browsers with Flutter web support

### Architecture Patterns

- **State Management:** Provider pattern for theme and settings
- **Database:** Sembast for local data persistence
- **Navigation:** Material PageRoute with proper lifecycle management
- **Responsive Design:** Adaptive layouts with breakpoint-based adjustments

### Future Enhancements

- **Accessibility:** Enhanced screen reader support and high contrast modes
- **Performance:** Further optimization of table rendering for large datasets
- **Customization:** User-defined color themes and spacing preferences
- **Analytics:** Advanced habit analytics with trend visualization

---

## Development Guidelines

### Code Style

- Use `GoogleFonts.inter()` and `GoogleFonts.robotoMono()` for consistent typography
- Implement `ModernTheme` spacing constants instead of hardcoded values
- Follow Material 3 design principles for component styling
- Use theme-aware colors that adapt to light/dark modes

### Component Creation

- Extend existing Modern\* components when possible
- Implement proper `dispose()` methods for stateful widgets
- Use `AnimationController` with appropriate `TickerProvider`
- Include accessibility features (semantic labels, touch targets)

### Testing Considerations

- Test theme switching functionality across all components
- Verify touch target sizes meet accessibility requirements
- Validate color contrast ratios in both light and dark themes
- Test table performance with large datasets (100+ habits)

---

_Last Updated: 9 Aug 2025_
_Next Review: Nov 2025_
