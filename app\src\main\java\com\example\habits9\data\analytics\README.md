# Habit Analytics System

This package contains all the business logic for calculating and exposing analytics for individual habits. The implementation supports both Yes/No habits and measurable (numerical) habits.

## Architecture

The analytics system follows a clean architecture pattern:

```
UI Layer (ViewModel) → Repository → Use Case → Data Sources
```

### Components

1. **Data Models** (`HabitAnalytics.kt`)
   - `YesNoHabitAnalytics`: Analytics for Yes/No habits
   - `MeasurableHabitAnalytics`: Analytics for measurable habits
   - `ChartDataPoint`: Data structure for chart visualization
   - `TimePeriod`: Enum for time period aggregation
   - `GeneralInfo`: General information (current week/date)

2. **Use Case** (`HabitAnalyticsUseCase.kt`)
   - Contains all business logic for calculating metrics
   - Handles both habit types consistently
   - Provides granular functions for each metric

3. **Repository** (`HabitAnalyticsRepository.kt`)
   - Clean interface for analytics operations
   - Combines use case functions into cohesive analytics objects
   - Provides both aggregate and individual metric access

4. **ViewModel** (`HabitAnalyticsViewModel.kt`)
   - Exposes analytics data to UI layer
   - Manages loading states and error handling
   - Supports dynamic chart time period updates

## Core Metrics

### For Both Habit Types

- **Current Streak**: Consecutive days completed from today backwards
- **Longest Streak**: Longest streak in habit's entire history
- **Completion Rate**: (Total Completions / Total Scheduled Days) × 100
- **Total Completions**: Total number of times habit was completed

### For Measurable Habits Only

- **Total Amount**: Sum of all numerical values recorded
- **Average per Completion**: Total Amount ÷ Total Completions
- **Best Day**: Highest numerical value ever recorded

## Chart Data

### Completion History
- Supports WEEK, MONTH, QUARTER, YEAR time periods
- Yes/No habits: Returns count of completions per period
- Measurable habits: Returns sum of values per period
- Returns `List<ChartDataPoint>` with labels and values

### Calendar Data (Heatmap)
- Covers last 6 months (~180 days)
- Yes/No habits: Returns `Map<LocalDate, Boolean>`
- Measurable habits: Returns `Map<LocalDate, Float>`

## Usage Examples

### In a ViewModel

```kotlin
@HiltViewModel
class MyViewModel @Inject constructor(
    private val analyticsRepository: HabitAnalyticsRepository
) : ViewModel() {

    fun loadAnalytics(habitId: Long, habitType: HabitType) {
        viewModelScope.launch {
            when (habitType) {
                HabitType.YES_NO -> {
                    val analytics = analyticsRepository.getYesNoHabitAnalytics(habitId)
                    // Use analytics data
                }
                HabitType.NUMERICAL -> {
                    val analytics = analyticsRepository.getMeasurableHabitAnalytics(habitId)
                    // Use analytics data
                }
            }
        }
    }
}
```

### Individual Metrics

```kotlin
// Get specific metrics
val currentStreak = analyticsRepository.getCurrentStreak(habitId)
val completionRate = analyticsRepository.getCompletionRate(habitId)
val chartData = analyticsRepository.getCompletionHistory(habitId, TimePeriod.MONTH)
```

### General Information

```kotlin
val generalInfo = analyticsRepository.getGeneralInfo()
println("Current Week: ${generalInfo.currentWeek}") // e.g., "W34"
println("Current Date: ${generalInfo.currentDate}") // e.g., "Sat, 09 Aug"
```

## Key Implementation Details

### Streak Calculation
- Only counts scheduled days (uses `HabitScheduler.isHabitScheduled()`)
- Breaks on first scheduled but incomplete day
- Handles different habit frequencies correctly

### Completion Detection
- Yes/No habits: Any completion record = completed
- Measurable habits: Value meets target criteria (AT_LEAST/AT_MOST)

### Data Consistency
- Reuses existing scheduling logic (`HabitScheduler`)
- Consistent with existing completion tracking in `MainViewModel`
- Follows same patterns as reference project

## Testing

The system includes comprehensive unit tests (`HabitAnalyticsUseCaseTest.kt`) that verify:
- Current streak calculation with missed days
- Completion rate calculation (83.3% for 25/30 days)
- Measurable habit metrics (total: 75, average: 25, best: 30)
- General information formatting

## Verification

Use `AnalyticsVerification.runAllVerifications()` to manually test all calculations with sample data.

## Integration

The analytics system is designed to integrate seamlessly with the existing habit details screen. The `HabitAnalyticsViewModel` can be injected into any Compose screen that needs analytics data.

## Future Extensions

The system is designed to be extensible:
- Additional time periods can be added to `TimePeriod` enum
- New metrics can be added to the use case
- Chart data format can be customized for different visualization needs
- Calendar data can support additional value types
