# Prompt 5.7: Replace Score Chart with Performance Heatmap

## A. The Objective & Context

The goal of this task is to completely remove the "Score" line chart and replace it with a more intuitive **Performance Heatmap**. This new component will display performance over various timeframes using a simple, color-coded block system, which will be much easier for users to interpret quickly.

This task involves removing the old chart component, creating the new heatmap UI, and connecting it to the existing data logic in the `ViewModel`.

## B. Detailed Implementation Plan

### 1. Remove the Existing "Score" Chart
- From the `ShowHabitActivity` layout file, find the `CardView` containing the "Score" line chart and its associated `Spinner`.
- **Completely delete this entire component** from the layout.

### 2. Implement the "Performance" Heatmap Card
- In place of the old chart, create a new `CardView`.
- **Title**: The card should have a `TextView` with the title "Performance".
- **Filter**: Add a `Spinner` or `DropDownMenu` to the top-right of the card. It must contain the following options: **"Day", "Week", "Month", "Quarter",** and **"Year."**

### 3. Implement the Heatmap Block View
- Inside the card, you will render a series of colored blocks. A `RecyclerView` with a `GridLayoutManager` is the recommended approach for this.
- Each block represents a single time period (e.g., one block for "August," one for "July," etc.).
- Each block should contain two pieces of text:
    - The period label (e.g., "Aug" or "W32").
    - The performance score for that period (e.g., "85%").

### 4. Implement Color-Coding Logic
- The background color of each block **must** be determined by its performance score. You must implement the following color scale:
    - **Score > 90%**: Dark Green (or a dark, saturated `accent-primary` color)
    - **Score 70-89%**: Light Green (or a standard `accent-primary` color)
    - **Score 50-69%**: Yellow
    - **Score < 50%**: Red
- These colors must be defined properly to support both **Light and Dark themes**.

### 5. Connect to the ViewModel
- The data for this component is already available from the `ViewModel`.
- When the user selects a time period from the filter (e.g., "Month"), you must call the `getCompletionHistory()` function (or a similar appropriate function) from the `ViewModel` with the selected `TimePeriod`.
- The list of data returned from the `ViewModel` should be passed to the `RecyclerView` adapter, which will then render the correct number of blocks with the correct labels, scores, and background colors.
- The heatmap must update instantly whenever the filter is changed.

## C. Meticulous Verification Plan

1.  **Visual Replacement Verification**:
    - **CRITICAL**: Launch the Habit Details screen. Verify the old line chart is completely gone and the new "Performance" heatmap card is displayed in its place.
    - Confirm the card has the title "Performance" and the filter `Spinner` with the correct five options.

2.  **Functional Verification**:
    - Select "Month" from the filter. **CRITICAL**: The view must update to show a series of blocks, one for each of the recent months.
    - Select "Week". The view must update to show blocks for each of the recent weeks.
    - Test all five filter options ("Day", "Week", "Month", "Quarter", "Year") and confirm the heatmap updates correctly with the relevant data for each.

3.  **Color-Coding Verification**:
    - Create a test habit where last month's score was 95% and this month's score is 45%.
    - **CRITICAL**: Verify that the block for last month is **Dark Green** and the block for this month is **Red**.
    - Test other score ranges to ensure the Yellow and Light Green colors are also applied correctly.

4.  **Data Display Verification**:
    - For each block, verify that it correctly displays both the time period label (e.g., "Aug") and the accurate score (e.g., "45%").

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.