package com.example.habits9.ui.details.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.habits9.data.analytics.TimePeriod
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.*
import kotlin.math.max
import kotlin.math.min

data class ScorePoint(
    val timestamp: Long,
    val value: Double // 0.0 to 1.0 representing 0% to 100%
)

@Composable
fun ScoreChart(
    scores: List<ScorePoint>,
    selectedTimePeriod: TimePeriod,
    onTimePeriodChanged: (TimePeriod) -> Unit,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary
) {
    val context = LocalContext.current
    var expanded by remember { mutableStateOf(false) }

    // Theme colors
    val backgroundColor = MaterialTheme.colorScheme.surface
    val textColor = MaterialTheme.colorScheme.onSurface
    val accentColor = MaterialTheme.colorScheme.primary
    val secondaryTextColor = MaterialTheme.colorScheme.onSurfaceVariant

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp) // --padding-card from style guide
        ) {
            // Header with title and time period filter
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Score",
                    color = textColor,
                    fontSize = 14.sp, // display-small from style guide
                    fontFamily = FontFamily.Default, // Inter font family
                    fontWeight = FontWeight.SemiBold, // 600 weight from style guide
                    letterSpacing = (-0.01f).sp // -0.01em from style guide
                )

                // Time period dropdown
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = selectedTimePeriod.name.lowercase().replaceFirstChar { it.uppercase() },
                        onValueChange = { },
                        readOnly = true,
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .width(120.dp) // Increased width
                            .height(48.dp), // Increased height
                        textStyle = androidx.compose.ui.text.TextStyle(
                            fontSize = 12.sp, // Increased font size for better visibility
                            fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
                            color = secondaryTextColor,
                            letterSpacing = 0.05.sp // 0.05em from style guide
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = accentColor,
                            unfocusedBorderColor = secondaryTextColor
                        )
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        TimePeriod.values().forEach { period ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = period.name.lowercase().replaceFirstChar { it.uppercase() },
                                        fontSize = 12.sp, // Increased font size for better visibility
                                        fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
                                        color = textColor,
                                        letterSpacing = 0.05.sp // 0.05em from style guide
                                    )
                                },
                                onClick = {
                                    onTimePeriodChanged(period)
                                    expanded = false
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp)) // space-sm from style guide

            // Score Chart Canvas - Compact and Sleek (Reference Implementation Style)
            val textMeasurer = rememberTextMeasurer()
            val scrollState = rememberScrollState()

            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(180.dp) // Compact height like reference
                    .horizontalScroll(scrollState)
                    .padding(horizontal = 4.dp, vertical = 2.dp) // Minimal padding
            ) {
                if (scores.isEmpty()) return@Canvas

                drawCompactScoreChart(
                    scores = scores,
                    color = accentColor,
                    textColor = textColor,
                    secondaryTextColor = secondaryTextColor,
                    canvasWidth = size.width,
                    canvasHeight = size.height,
                    textMeasurer = textMeasurer,
                    timePeriod = selectedTimePeriod,
                    scrollState = scrollState
                )
            }
        }
    }
}

/**
 * Compact Score Chart implementation based on reference project
 * Follows the exact design principles from the original uHabits implementation
 */
private fun DrawScope.drawCompactScoreChart(
    scores: List<ScorePoint>,
    color: Color,
    textColor: Color,
    secondaryTextColor: Color,
    canvasWidth: Float,
    canvasHeight: Float,
    textMeasurer: TextMeasurer,
    timePeriod: TimePeriod,
    scrollState: androidx.compose.foundation.ScrollState
) {
    if (scores.isEmpty()) return

    // Reference implementation dimensions (compact and sleek)
    val tinyTextSize = 10.sp.toPx() // Reference: R.dimen.tinyTextSize
    val textSize = minOf(canvasHeight * 0.06f, tinyTextSize)
    val em = textSize * 1.2f // Font spacing equivalent

    // Layout calculations (following reference implementation)
    val footerHeight = (3 * em).toInt()
    val internalPaddingTop = em.toInt()
    val dataAreaHeight = canvasHeight - footerHeight - internalPaddingTop
    val baseSize = dataAreaHeight / 8f // Reference: 8 rows for data area
    val columnHeight = 8 * baseSize

    // Column width calculation (reference implementation style)
    val minColumnWidth = baseSize
    val maxTextWidth = measureMaxTextWidth(textMeasurer, textSize, timePeriod)
    val columnWidth = maxOf(minColumnWidth, maxTextWidth * 1.5f)

    // Number of visible columns
    val nColumns = maxOf(1, (canvasWidth / columnWidth).toInt())
    val adjustedColumnWidth = canvasWidth / nColumns.toFloat()

    // Data offset for scrolling (simplified)
    val dataOffset = (scrollState.value / adjustedColumnWidth).toInt()

    // Main grid rectangle (reference implementation)
    val gridRect = androidx.compose.ui.geometry.Rect(
        left = 0f,
        top = internalPaddingTop.toFloat(),
        right = canvasWidth,
        bottom = internalPaddingTop.toFloat() + columnHeight
    )

    // Draw 5-row grid with Y-axis labels (reference implementation)
    drawCompactGrid(
        gridRect = gridRect,
        textColor = secondaryTextColor,
        gridColor = secondaryTextColor.copy(alpha = 0.3f),
        textMeasurer = textMeasurer,
        textSize = textSize,
        em = em
    )

    // Draw data bars (bar chart style like reference image)
    for (k in 0 until nColumns) {
        val offset = nColumns - k - 1 + dataOffset
        if (offset >= scores.size) continue

        val score = scores[offset]
        val barWidth = adjustedColumnWidth * 0.6f // 60% of column width for bar
        val barLeft = k * adjustedColumnWidth + (adjustedColumnWidth - barWidth) / 2
        val barRight = barLeft + barWidth

        // Calculate bar height based on score value
        val barHeight = (columnHeight * score.value).toFloat()
        val barTop = internalPaddingTop + columnHeight - barHeight
        val barBottom = internalPaddingTop.toFloat() + columnHeight

        // Draw the vertical bar
        drawRect(
            color = color,
            topLeft = Offset(barLeft, barTop),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )

        // Draw footer label for this column
        drawCompactFooter(
            columnRect = androidx.compose.ui.geometry.Rect(
                left = k * adjustedColumnWidth,
                top = internalPaddingTop.toFloat(),
                right = (k + 1) * adjustedColumnWidth,
                bottom = internalPaddingTop.toFloat() + columnHeight
            ),
            timestamp = score.timestamp,
            timePeriod = timePeriod,
            textColor = textColor,
            textMeasurer = textMeasurer,
            textSize = textSize,
            em = em,
            footerHeight = footerHeight
        )
    }
}

/**
 * Measure maximum text width for column sizing (reference implementation)
 */
private fun measureMaxTextWidth(
    textMeasurer: TextMeasurer,
    textSize: Float,
    timePeriod: TimePeriod
): Float {
    val textStyle = TextStyle(
        fontSize = textSize.sp,
        fontFamily = FontFamily.Monospace
    )

    return when (timePeriod) {
        TimePeriod.WEEK -> {
            // Measure week numbers W01-W53
            (1..53).maxOf { week ->
                textMeasurer.measure("W$week", textStyle).size.width.toFloat()
            }
        }
        TimePeriod.MONTH -> {
            // Measure month abbreviations
            listOf("Jan", "Feb", "Mar", "Apr", "May", "Jun",
                   "Jul", "Aug", "Sep", "Oct", "Nov", "Dec").maxOf { month ->
                textMeasurer.measure(month, textStyle).size.width.toFloat()
            }
        }
        TimePeriod.QUARTER -> {
            textMeasurer.measure("Q4 24", textStyle).size.width.toFloat()
        }
        TimePeriod.YEAR -> {
            textMeasurer.measure("2024", textStyle).size.width.toFloat()
        }
    }
}

/**
 * Draw 5-row grid with Y-axis labels (reference implementation)
 */
private fun DrawScope.drawCompactGrid(
    gridRect: androidx.compose.ui.geometry.Rect,
    textColor: Color,
    gridColor: Color,
    textMeasurer: TextMeasurer,
    textSize: Float,
    em: Float
) {
    val nRows = 5
    // No change needed here, but ensure the context provides clear types.
    val rowHeight = gridRect.height / nRows.toFloat()

    for (i in 0 until nRows) {
        // The original calculation for percentage was correct for the loop.
        // The user's suggested fix `100 - (i * 100 / nRows)` would use integer division,
        // which would result in less precise values (e.g., 100, 80, 60, 40, 20).
        // Let's stick to a floating-point calculation for accuracy.
        val percentage = 100f - (i.toFloat() / nRows.toFloat()) * 100f

        // FIX: Use parentheses to resolve operator precedence and reduce ambiguity.
        val y = gridRect.top + (i.toFloat() * rowHeight)

        // Draw percentage label
        // FIX: Explicitly convert the number to a String before templating.
        val labelText = "${percentage.toInt()}%"
        val textLayoutResult = textMeasurer.measure(
            text = labelText,
            style = TextStyle(
                fontSize = textSize.sp,
                color = textColor,
                fontFamily = FontFamily.Monospace
            )
        )

        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(
                x = gridRect.left + (0.5f * em),
                // FIX: Use parentheses here as well for clarity.
                y = y + (1.0f * em)
            )
        )

        // Draw horizontal grid line
        drawLine(
            color = gridColor,
            start = Offset(gridRect.left, y),
            end = Offset(gridRect.right, y),
            strokeWidth = 1f
        )
    }

    // Draw final bottom line
    drawLine(
        color = gridColor,
        start = Offset(gridRect.left, gridRect.bottom),
        end = Offset(gridRect.right, gridRect.bottom),
        strokeWidth = 1f
    )
}



/**
 * Draw footer labels for each column (reference implementation)
 */
private fun DrawScope.drawCompactFooter(
    columnRect: androidx.compose.ui.geometry.Rect,
    timestamp: Long,
    timePeriod: TimePeriod,
    textColor: Color,
    textMeasurer: TextMeasurer,
    textSize: Float,
    em: Float,
    footerHeight: Int
) {
    val labelText = formatCompactTimePeriodLabel(timestamp, timePeriod)

    val textLayoutResult = textMeasurer.measure(
        text = labelText,
        style = TextStyle(
            fontSize = textSize.sp,
            color = textColor,
            fontFamily = FontFamily.Monospace
        )
    )

    // Center horizontally, position at bottom
    val centerX = (columnRect.left + columnRect.right) / 2f
    drawText(
        textLayoutResult = textLayoutResult,
        topLeft = Offset(
            x = centerX - textLayoutResult.size.width / 2f,
            y = columnRect.bottom + 1.2f * em
        )
    )
}

/**
 * Format timestamp for compact display (reference implementation)
 */
private fun formatCompactTimePeriodLabel(timestamp: Long, timePeriod: TimePeriod): String {
    val date = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate()

    return when (timePeriod) {
        TimePeriod.WEEK -> {
            val weekFields = WeekFields.of(Locale.getDefault())
            val weekNumber = date.get(weekFields.weekOfYear())
            "W$weekNumber"
        }
        TimePeriod.MONTH -> {
            date.format(DateTimeFormatter.ofPattern("MMM"))
        }
        TimePeriod.QUARTER -> {
            val quarter = (date.monthValue - 1) / 3 + 1
            "Q$quarter"
        }
        TimePeriod.YEAR -> {
            date.year.toString()
        }
    }
}

private fun DrawScope.drawGridWithLabels(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    chartBottom: Float,
    color: Color,
    textColor: Color,
    textMeasurer: TextMeasurer
) {
    // Draw horizontal grid lines and Y-axis labels (for percentage values)
    for (i in 0..4) {
        val y = chartTop + (i.toFloat() * chartHeight / 4f)
        val percentage = (100 - (i * 25)) // 100%, 75%, 50%, 25%, 0%

        // Draw grid line
        drawLine(
            color = color,
            start = Offset(chartLeft, y),
            end = Offset(chartLeft + chartWidth, y),
            strokeWidth = 1.5f, // Slightly thicker grid lines
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(8f, 8f)) // Larger dash pattern
        )

        // Draw Y-axis label
        val labelText = "${percentage}%"
        val textLayoutResult = textMeasurer.measure(
            text = labelText,
            style = TextStyle(
                fontSize = 14.sp, // Increased font size for better readability
                color = textColor,
                fontFamily = FontFamily.Monospace,
                fontWeight = FontWeight.Medium
            )
        )

        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(
                x = chartLeft - textLayoutResult.size.width - 8f,
                y = y - textLayoutResult.size.height / 2
            )
        )
    }
}

private fun DrawScope.drawXAxisLabels(
    scores: List<ScorePoint>,
    points: List<Offset>,
    chartBottom: Float,
    textColor: Color,
    textMeasurer: TextMeasurer,
    timePeriod: TimePeriod
) {
    if (scores.isEmpty() || points.isEmpty()) return

    // Show alternating labels to avoid overcrowding
    scores.forEachIndexed { index, score ->
        // Show every other label (alternating)
        if (index % 2 == 0 && index < points.size) {
            val point = points[index]
            val labelText = formatTimePeriodLabel(score.timestamp, timePeriod)

            val textLayoutResult = textMeasurer.measure(
                text = labelText,
                style = TextStyle(
                    fontSize = 12.sp, // Increased font size for better readability
                    color = textColor,
                    fontFamily = FontFamily.Monospace,
                    fontWeight = FontWeight.Medium
                )
            )

            drawText(
                textLayoutResult = textLayoutResult,
                topLeft = Offset(
                    x = point.x - textLayoutResult.size.width / 2f,
                    y = chartBottom + 12f // Increased spacing
                )
            )
        }
    }
}

/**
 * Format timestamp according to the time period type
 */
private fun formatTimePeriodLabel(timestamp: Long, timePeriod: TimePeriod): String {
    val date = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate()

    return when (timePeriod) {
        TimePeriod.WEEK -> {
            val weekFields = WeekFields.of(Locale.getDefault())
            val weekNumber = date.get(weekFields.weekOfYear())
            "W$weekNumber"
        }
        TimePeriod.MONTH -> {
            date.format(DateTimeFormatter.ofPattern("MMM yy"))
        }
        TimePeriod.QUARTER -> {
            val quarter = (date.monthValue - 1) / 3 + 1
            "Q$quarter ${date.year.toString().takeLast(2)}"
        }
        TimePeriod.YEAR -> {
            date.year.toString()
        }
    }
}

private fun DrawScope.drawDataPointLabels(
    scores: List<ScorePoint>,
    points: List<Offset>,
    textColor: Color,
    textMeasurer: TextMeasurer
) {
    scores.forEachIndexed { index, score ->
        if (index < points.size) {
            val point = points[index]
            val percentage = (score.value * 100).toInt()
            val labelText = "${percentage}%"

            val textLayoutResult = textMeasurer.measure(
                text = labelText,
                style = TextStyle(
                    fontSize = 11.sp, // Increased font size for better readability
                    color = textColor,
                    fontFamily = FontFamily.Monospace,
                    fontWeight = FontWeight.Bold
                )
            )

            // Position label above the point with more spacing
            drawText(
                textLayoutResult = textLayoutResult,
                topLeft = Offset(
                    x = point.x - textLayoutResult.size.width / 2f,
                    y = point.y - textLayoutResult.size.height - 12f // Increased spacing
                )
            )
        }
    }
}

private fun DrawScope.drawScoreLine(points: List<Offset>, color: Color) {
    if (points.size < 2) return

    val path = Path()
    path.moveTo(points[0].x, points[0].y)

    for (i in 1 until points.size) {
        path.lineTo(points[i].x, points[i].y)
    }

    drawPath(
        path = path,
        color = color,
        style = Stroke(
            width = 4f, // Increased line width for better visibility
            cap = StrokeCap.Round,
            join = StrokeJoin.Round
        )
    )
}

private fun DrawScope.drawScorePoints(points: List<Offset>, color: Color) {
    points.forEach { point ->
        // Draw larger outer circle
        drawCircle(
            color = color,
            radius = 6f, // Increased radius for better visibility
            center = point
        )
        // Draw white inner circle for better visibility
        drawCircle(
            color = Color.White,
            radius = 3f, // Increased inner radius proportionally
            center = point
        )
    }
}
