package com.example.habits9.data.analytics

import com.example.habits9.data.Completion
import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import java.time.LocalDate
import java.time.ZoneId

/**
 * Unit tests for HabitAnalyticsUseCase.
 * Verifies all analytics calculations work correctly.
 */
class HabitAnalyticsUseCaseTest {

    private lateinit var habitRepository: HabitRepository
    private lateinit var completionRepository: CompletionRepository
    private lateinit var analyticsUseCase: HabitAnalyticsUseCase

    private val testHabitId = 1L
    private val today = LocalDate.now()

    @Before
    fun setup() {
        habitRepository = mockk()
        completionRepository = mockk()
        analyticsUseCase = HabitAnalyticsUseCase(habitRepository, completionRepository)
    }

    @Test
    fun `getCurrentStreak should return 7 when last 7 days completed but day 8 missed`() = runTest {
        // Create test habit (daily Yes/No habit)
        val testHabit = createTestYesNoHabit()
        coEvery { habitRepository.getAllHabits() } returns flowOf(listOf(testHabit))

        // Create completions for last 10 days, but miss day 8 (2 days ago)
        val completions = mutableListOf<Completion>()
        for (i in 0..9) {
            if (i != 8) { // Miss day 8
                val date = today.minusDays(i.toLong())
                val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
                completions.add(Completion(
                    id = "completion_$i",
                    habitId = testHabitId,
                    timestamp = dayStart
                ))
            }
        }
        coEvery { completionRepository.getCompletionsForHabit(testHabitId) } returns flowOf(completions)

        val result = analyticsUseCase.getCurrentStreak(testHabitId)
        assertEquals(7, result)
    }

    @Test
    fun `getCompletionRate should return 83_3 percent when 25 out of 30 days completed`() = runTest {
        val testHabit = createTestYesNoHabit()
        coEvery { habitRepository.getAllHabits() } returns flowOf(listOf(testHabit))

        // Create 25 completions out of 30 scheduled days
        val completions = mutableListOf<Completion>()
        for (i in 0 until 25) {
            val date = today.minusDays(i.toLong())
            val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
            completions.add(Completion(
                id = "completion_$i",
                habitId = testHabitId,
                timestamp = dayStart
            ))
        }
        coEvery { completionRepository.getCompletionsForHabit(testHabitId) } returns flowOf(completions)

        val result = analyticsUseCase.getCompletionRate(testHabitId)
        assertEquals(83.3f, result, 0.1f)
    }

    @Test
    fun `getTotalAmount should return 75 for measurable habit with values 20, 30, 25`() = runTest {
        val testHabit = createTestMeasurableHabit()
        coEvery { habitRepository.getAllHabits() } returns flowOf(listOf(testHabit))

        val completions = listOf(
            Completion(id = "1", habitId = testHabitId, timestamp = getDayStart(today), value = "20"),
            Completion(id = "2", habitId = testHabitId, timestamp = getDayStart(today.minusDays(1)), value = "30"),
            Completion(id = "3", habitId = testHabitId, timestamp = getDayStart(today.minusDays(2)), value = "25")
        )
        coEvery { completionRepository.getCompletionsForHabit(testHabitId) } returns flowOf(completions)

        val result = analyticsUseCase.getTotalAmount(testHabitId)
        assertEquals(75.0, result, 0.01)
    }

    @Test
    fun `getAveragePerCompletion should return 25 for total 75 with 3 completions`() = runTest {
        val testHabit = createTestMeasurableHabit()
        coEvery { habitRepository.getAllHabits() } returns flowOf(listOf(testHabit))

        val completions = listOf(
            Completion(id = "1", habitId = testHabitId, timestamp = getDayStart(today), value = "20"),
            Completion(id = "2", habitId = testHabitId, timestamp = getDayStart(today.minusDays(1)), value = "30"),
            Completion(id = "3", habitId = testHabitId, timestamp = getDayStart(today.minusDays(2)), value = "25")
        )
        coEvery { completionRepository.getCompletionsForHabit(testHabitId) } returns flowOf(completions)

        val result = analyticsUseCase.getAveragePerCompletion(testHabitId)
        assertEquals(25.0, result, 0.01)
    }

    @Test
    fun `getBestDay should return 30 for values 20, 30, 25`() = runTest {
        val testHabit = createTestMeasurableHabit()
        coEvery { habitRepository.getAllHabits() } returns flowOf(listOf(testHabit))

        val completions = listOf(
            Completion(id = "1", habitId = testHabitId, timestamp = getDayStart(today), value = "20"),
            Completion(id = "2", habitId = testHabitId, timestamp = getDayStart(today.minusDays(1)), value = "30"),
            Completion(id = "3", habitId = testHabitId, timestamp = getDayStart(today.minusDays(2)), value = "25")
        )
        coEvery { completionRepository.getCompletionsForHabit(testHabitId) } returns flowOf(completions)

        val result = analyticsUseCase.getBestDay(testHabitId)
        assertEquals(30.0, result, 0.01)
    }

    @Test
    fun `getGeneralInfo should return correct week and date format`() {
        val result = analyticsUseCase.getGeneralInfo()
        
        // Verify week format (e.g., "W34")
        assert(result.currentWeek.startsWith("W"))
        assert(result.currentWeek.length >= 2)
        
        // Verify date format (e.g., "Sat, 09 Aug")
        assert(result.currentDate.contains(","))
        assert(result.currentDate.length >= 10)
    }

    private fun createTestYesNoHabit(): Habit {
        return Habit(
            id = testHabitId,
            name = "Test Yes/No Habit",
            description = "Test habit for analytics",
            creationDate = today.minusDays(30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "YES_NO",
            frequencyType = "DAILY",
            repeatsEvery = 1
        )
    }

    private fun createTestMeasurableHabit(): Habit {
        return Habit(
            id = testHabitId,
            name = "Test Measurable Habit",
            description = "Test measurable habit for analytics",
            creationDate = today.minusDays(30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "NUMERICAL",
            targetType = NumericalHabitType.AT_LEAST.value,
            targetValue = 10.0,
            unit = "pages",
            frequencyType = "DAILY",
            repeatsEvery = 1
        )
    }

    private fun getDayStart(date: LocalDate): Long {
        val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
    }
}
