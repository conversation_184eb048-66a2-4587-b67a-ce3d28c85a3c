package com.example.habits9.data.analytics

import android.util.Log
import com.example.habits9.data.Completion
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.Locale

/**
 * Verification class for testing analytics calculations manually.
 * This can be used to verify the implementation meets the requirements.
 */
object AnalyticsVerification {

    private const val TAG = "AnalyticsVerification"

    /**
     * Verify general information formatting.
     */
    fun verifyGeneralInfo() {
        val today = LocalDate.now()
        val weekFields = WeekFields.of(Locale.getDefault())
        val weekNumber = today.get(weekFields.weekOfYear())
        val currentWeek = "W$weekNumber"
        
        val dateFormatter = DateTimeFormatter.ofPattern("EEE, dd MMM", Locale.getDefault())
        val currentDate = today.format(dateFormatter)
        
        Log.d(TAG, "Current Week: $currentWeek")
        Log.d(TAG, "Current Date: $currentDate")
        
        // Verify format
        assert(currentWeek.startsWith("W")) { "Week format should start with 'W'" }
        assert(currentDate.contains(",")) { "Date format should contain comma" }
        
        Log.d(TAG, "✓ General info verification passed")
    }

    /**
     * Verify Yes/No habit analytics with test data.
     */
    fun verifyYesNoHabitAnalytics() {
        Log.d(TAG, "Verifying Yes/No habit analytics...")
        
        // Create test habit
        val testHabit = createTestYesNoHabit()
        
        // Create test completions: last 10 days completed except day 8
        val completions = mutableListOf<Completion>()
        val today = LocalDate.now()
        
        for (i in 0..9) {
            if (i != 8) { // Miss day 8
                val date = today.minusDays(i.toLong())
                val timestamp = getDayStart(date)
                completions.add(Completion(
                    id = "test_$i",
                    habitId = 1L,
                    timestamp = timestamp
                ))
            }
        }
        
        // Manually calculate expected values
        val expectedCurrentStreak = 7 // Days 0-6 completed, day 7 completed, day 8 missed
        val expectedTotalCompletions = 9 // 10 days minus 1 missed day
        
        Log.d(TAG, "Expected current streak: $expectedCurrentStreak")
        Log.d(TAG, "Expected total completions: $expectedTotalCompletions")
        Log.d(TAG, "Test completions created: ${completions.size}")
        
        Log.d(TAG, "✓ Yes/No habit verification data prepared")
    }

    /**
     * Verify measurable habit analytics with test data.
     */
    fun verifyMeasurableHabitAnalytics() {
        Log.d(TAG, "Verifying measurable habit analytics...")
        
        // Create test measurable habit
        val testHabit = createTestMeasurableHabit()
        
        // Create test completions: Day 1 (20), Day 2 (30), Day 3 (25)
        val completions = listOf(
            Completion(id = "1", habitId = 1L, timestamp = getDayStart(LocalDate.now()), value = "20"),
            Completion(id = "2", habitId = 1L, timestamp = getDayStart(LocalDate.now().minusDays(1)), value = "30"),
            Completion(id = "3", habitId = 1L, timestamp = getDayStart(LocalDate.now().minusDays(2)), value = "25")
        )
        
        // Calculate expected values
        val expectedTotalAmount = 75.0 // 20 + 30 + 25
        val expectedAveragePerCompletion = 25.0 // 75 / 3
        val expectedBestDay = 30.0 // Maximum value
        
        Log.d(TAG, "Expected total amount: $expectedTotalAmount")
        Log.d(TAG, "Expected average per completion: $expectedAveragePerCompletion")
        Log.d(TAG, "Expected best day: $expectedBestDay")
        
        // Verify calculations manually
        val actualTotalAmount = completions.sumOf { it.value?.toDoubleOrNull() ?: 0.0 }
        val actualAveragePerCompletion = actualTotalAmount / completions.size
        val actualBestDay = completions.mapNotNull { it.value?.toDoubleOrNull() }.maxOrNull() ?: 0.0
        
        assert(actualTotalAmount == expectedTotalAmount) { "Total amount calculation failed" }
        assert(actualAveragePerCompletion == expectedAveragePerCompletion) { "Average calculation failed" }
        assert(actualBestDay == expectedBestDay) { "Best day calculation failed" }
        
        Log.d(TAG, "✓ Measurable habit verification passed")
    }

    /**
     * Verify chart data formatting.
     */
    fun verifyChartDataFormatting() {
        Log.d(TAG, "Verifying chart data formatting...")
        
        val today = LocalDate.now()
        
        // Test different time period labels
        val weekLabel = formatWeekLabel(today)
        val monthLabel = formatMonthLabel(today)
        val quarterLabel = formatQuarterLabel(today)
        val yearLabel = formatYearLabel(today)
        
        Log.d(TAG, "Week label: $weekLabel")
        Log.d(TAG, "Month label: $monthLabel")
        Log.d(TAG, "Quarter label: $quarterLabel")
        Log.d(TAG, "Year label: $yearLabel")
        
        // Verify formats
        assert(weekLabel.startsWith("W")) { "Week label should start with 'W'" }
        assert(monthLabel.contains(" ")) { "Month label should contain space" }
        assert(quarterLabel.startsWith("Q")) { "Quarter label should start with 'Q'" }
        assert(yearLabel.length == 4) { "Year label should be 4 digits" }
        
        Log.d(TAG, "✓ Chart data formatting verification passed")
    }

    /**
     * Verify calendar data structure.
     */
    fun verifyCalendarDataStructure() {
        Log.d(TAG, "Verifying calendar data structure...")
        
        val today = LocalDate.now()
        val startDate = today.minusMonths(6)
        val expectedDays = startDate.until(today).days + 1
        
        Log.d(TAG, "Expected calendar days: $expectedDays (~180 days)")
        Log.d(TAG, "Start date: $startDate")
        Log.d(TAG, "End date: $today")
        
        // Verify we're covering approximately 6 months
        assert(expectedDays >= 180) { "Calendar should cover at least 180 days" }
        assert(expectedDays <= 190) { "Calendar should not exceed 190 days" }
        
        Log.d(TAG, "✓ Calendar data structure verification passed")
    }

    // Helper functions
    private fun createTestYesNoHabit(): Habit {
        return Habit(
            id = 1L,
            name = "Test Yes/No Habit",
            description = "Test habit for verification",
            creationDate = LocalDate.now().minusDays(30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "YES_NO",
            frequencyType = "DAILY",
            repeatsEvery = 1
        )
    }

    private fun createTestMeasurableHabit(): Habit {
        return Habit(
            id = 1L,
            name = "Pages Read",
            description = "Test measurable habit",
            creationDate = LocalDate.now().minusDays(30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "NUMERICAL",
            targetType = NumericalHabitType.AT_LEAST.value,
            targetValue = 10.0,
            unit = "pages",
            frequencyType = "DAILY",
            repeatsEvery = 1
        )
    }

    private fun getDayStart(date: LocalDate): Long {
        val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
    }

    private fun formatWeekLabel(date: LocalDate): String {
        val weekFields = WeekFields.of(Locale.getDefault())
        val weekNumber = date.get(weekFields.weekOfYear())
        return "W$weekNumber"
    }

    private fun formatMonthLabel(date: LocalDate): String {
        return date.format(DateTimeFormatter.ofPattern("MMM yyyy"))
    }

    private fun formatQuarterLabel(date: LocalDate): String {
        val quarter = (date.monthValue - 1) / 3 + 1
        return "Q$quarter ${date.year}"
    }

    private fun formatYearLabel(date: LocalDate): String {
        return date.year.toString()
    }

    /**
     * Run all verification tests.
     */
    fun runAllVerifications() {
        Log.d(TAG, "Starting analytics verification...")
        
        try {
            verifyGeneralInfo()
            verifyYesNoHabitAnalytics()
            verifyMeasurableHabitAnalytics()
            verifyChartDataFormatting()
            verifyCalendarDataStructure()
            
            Log.d(TAG, "🎉 All analytics verifications passed!")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Verification failed: ${e.message}", e)
        }
    }
}
