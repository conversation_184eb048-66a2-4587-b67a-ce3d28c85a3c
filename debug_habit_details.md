# Debug Guide for Habit Details Dynamic Data Issue

## Problem
The Habit Details screen shows "Loading..." in the toolbar and static data in the subtitle instead of dynamic habit-specific information.

## Changes Made

### 1. Added Debugging Logs
- **MainActivity.kt**: Added logging when navigating to habit details
- **HabitDetailsViewModel.kt**: Added comprehensive logging for data loading process
- **HabitRepository.kt**: Added logging for habit search and retrieval

### 2. Enhanced Error Handling
- **HabitDetailsScreen.kt**: Added error toast display and logging
- **HabitDetailsViewModel.kt**: Added timeout mechanism (10 seconds) for habit loading
- **HabitDetailsViewModel.kt**: Added fallback sample data when habit not found

### 3. Added Icons to Subtitle
- **HabitDetailsScreen.kt**: Added icons to frequency, week number, and date display:
  - Frequency: Refresh icon
  - Week Number: Schedule icon  
  - Date: DateRange icon

### 4. Improved Loading States
- **HabitDetailsScreen.kt**: Added proper loading state for subtitle area
- **HabitDetailsScreen.kt**: Better conditional rendering based on loading state

## How to Debug

1. **Check Logs**: Look for these log messages:
   - `MainActivity: Navigating to habit details with habitId: X`
   - `HabitDetailsScreen: Loading habit details for habitId: X`
   - `HabitDetailsViewModel: Available habits: [list of habits]`
   - `HabitRepository: getHabitById called with habitId: X`

2. **Common Issues**:
   - **No habits in database**: Check if user has created any habits
   - **ID mismatch**: The habitId being passed doesn't match any existing habit IDs
   - **Authentication issue**: User not logged in to Firebase
   - **Network issue**: Firestore connection problems

3. **Expected Behavior**:
   - Toolbar should show actual habit name instead of "Loading..."
   - Subtitle should show real frequency, current week number, and today's date
   - Icons should appear next to each subtitle element

## Testing Steps

1. Create a habit in the app first
2. Navigate to habit details from the home screen
3. Check logs to see what habitId is being passed
4. Verify that the habitId matches an existing habit in the database
5. If using fallback data, check the error message in the toast

## Next Steps if Still Not Working

1. Check Firebase authentication status
2. Verify Firestore database has habit data
3. Check if the hash-based ID system is working correctly
4. Consider adding a direct database query to verify habit existence
