package com.example.habits9.data.analytics

import com.example.habits9.data.HabitType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for habit analytics.
 * Provides a clean interface for all analytics operations.
 */
@Singleton
class HabitAnalyticsRepository @Inject constructor(
    private val analyticsUseCase: HabitAnalyticsUseCase
) {

    /**
     * Get complete analytics for a Yes/No habit.
     */
    suspend fun getYesNoHabitAnalytics(habitId: Long): YesNoHabitAnalytics {
        return YesNoHabitAnalytics(
            currentStreak = analyticsUseCase.getCurrentStreak(habitId),
            longestStreak = analyticsUseCase.getLongestStreak(habitId),
            completionRate = analyticsUseCase.getCompletionRate(habitId),
            totalCompletions = analyticsUseCase.getTotalCompletions(habitId),
            completionHistory = analyticsUseCase.getCompletionHistory(habitId, TimePeriod.MONTH),
            scoreHistory = analyticsUseCase.getScoreHistory(habitId, TimePeriod.MONTH),
            calendarData = analyticsUseCase.getCalendarData(habitId)
                .mapValues { (_, value) -> value as Boolean }
        )
    }

    /**
     * Get complete analytics for a measurable habit.
     */
    suspend fun getMeasurableHabitAnalytics(habitId: Long): MeasurableHabitAnalytics {
        return MeasurableHabitAnalytics(
            currentStreak = analyticsUseCase.getCurrentStreak(habitId),
            longestStreak = analyticsUseCase.getLongestStreak(habitId),
            completionRate = analyticsUseCase.getCompletionRate(habitId),
            totalCompletions = analyticsUseCase.getTotalCompletions(habitId),
            totalAmount = analyticsUseCase.getTotalAmount(habitId),
            averagePerCompletion = analyticsUseCase.getAveragePerCompletion(habitId),
            bestDay = analyticsUseCase.getBestDay(habitId),
            completionHistory = analyticsUseCase.getCompletionHistory(habitId, TimePeriod.MONTH),
            scoreHistory = analyticsUseCase.getScoreHistory(habitId, TimePeriod.MONTH),
            calendarData = analyticsUseCase.getCalendarData(habitId)
                .mapValues { (_, value) -> value as Float }
        )
    }

    /**
     * Get analytics for any habit type.
     */
    suspend fun getHabitAnalytics(habitId: Long, habitType: HabitType): Any {
        return when (habitType) {
            HabitType.YES_NO -> getYesNoHabitAnalytics(habitId)
            HabitType.NUMERICAL -> getMeasurableHabitAnalytics(habitId)
        }
    }

    /**
     * Get general information.
     */
    fun getGeneralInfo(): GeneralInfo {
        return analyticsUseCase.getGeneralInfo()
    }

    /**
     * Get completion history for a specific time period.
     */
    suspend fun getCompletionHistory(habitId: Long, timePeriod: TimePeriod) =
        analyticsUseCase.getCompletionHistory(habitId, timePeriod)

    /**
     * Get score history for a specific time period.
     */
    suspend fun getScoreHistory(habitId: Long, timePeriod: TimePeriod) =
        analyticsUseCase.getScoreHistory(habitId, timePeriod)

    /**
     * Get calendar data for heatmap.
     */
    suspend fun getCalendarData(habitId: Long) = 
        analyticsUseCase.getCalendarData(habitId)

    // Individual metric functions for granular access
    suspend fun getCurrentStreak(habitId: Long) = analyticsUseCase.getCurrentStreak(habitId)
    suspend fun getLongestStreak(habitId: Long) = analyticsUseCase.getLongestStreak(habitId)
    suspend fun getCompletionRate(habitId: Long) = analyticsUseCase.getCompletionRate(habitId)
    suspend fun getTotalCompletions(habitId: Long) = analyticsUseCase.getTotalCompletions(habitId)
    suspend fun getTotalAmount(habitId: Long) = analyticsUseCase.getTotalAmount(habitId)
    suspend fun getAveragePerCompletion(habitId: Long) = analyticsUseCase.getAveragePerCompletion(habitId)
    suspend fun getBestDay(habitId: Long) = analyticsUseCase.getBestDay(habitId)
}
