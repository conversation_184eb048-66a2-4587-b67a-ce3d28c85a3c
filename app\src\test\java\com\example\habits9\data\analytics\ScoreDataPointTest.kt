package com.example.habits9.data.analytics

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for ScoreDataPoint.
 */
class ScoreDataPointTest {

    @Test
    fun scoreDataPoint_creation_isCorrect() {
        val timestamp = System.currentTimeMillis()
        val score = 0.85
        
        val scoreDataPoint = ScoreDataPoint(timestamp, score)
        
        assertEquals(timestamp, scoreDataPoint.timestamp)
        assertEquals(score, scoreDataPoint.score, 0.001)
    }

    @Test
    fun scoreDataPoint_scoreRange_isValid() {
        val timestamp = System.currentTimeMillis()
        
        // Test valid score range (0.0 to 1.0 representing 0% to 100%)
        val validScores = listOf(0.0, 0.1, 0.33, 0.5, 0.67, 0.9, 1.0)
        
        validScores.forEach { score ->
            val scoreDataPoint = ScoreDataPoint(timestamp, score)
            assertTrue("Score $score should be between 0.0 and 1.0", 
                scoreDataPoint.score >= 0.0 && scoreDataPoint.score <= 1.0)
        }
    }

    @Test
    fun scoreDataPoint_timestampHandling_isCorrect() {
        val currentTime = System.currentTimeMillis()
        val pastTime = currentTime - (24 * 60 * 60 * 1000) // 1 day ago
        val futureTime = currentTime + (24 * 60 * 60 * 1000) // 1 day from now
        
        val currentScore = ScoreDataPoint(currentTime, 0.5)
        val pastScore = ScoreDataPoint(pastTime, 0.3)
        val futureScore = ScoreDataPoint(futureTime, 0.8)
        
        assertEquals(currentTime, currentScore.timestamp)
        assertEquals(pastTime, pastScore.timestamp)
        assertEquals(futureTime, futureScore.timestamp)
        
        assertTrue("Past timestamp should be less than current", pastTime < currentTime)
        assertTrue("Future timestamp should be greater than current", futureTime > currentTime)
    }

    @Test
    fun scoreDataPoint_equality_worksCorrectly() {
        val timestamp = System.currentTimeMillis()
        val score = 0.75
        
        val scorePoint1 = ScoreDataPoint(timestamp, score)
        val scorePoint2 = ScoreDataPoint(timestamp, score)
        val scorePoint3 = ScoreDataPoint(timestamp + 1000, score)
        val scorePoint4 = ScoreDataPoint(timestamp, score + 0.1)
        
        assertEquals(scorePoint1, scorePoint2)
        assertNotEquals(scorePoint1, scorePoint3)
        assertNotEquals(scorePoint1, scorePoint4)
    }
}
