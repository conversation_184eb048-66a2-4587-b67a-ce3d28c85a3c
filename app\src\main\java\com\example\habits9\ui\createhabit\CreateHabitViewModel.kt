package com.example.habits9.ui.createhabit

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSection
import com.example.habits9.data.HabitSectionRepository
import com.example.habits9.data.EnhancedFrequency
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalTime
import javax.inject.Inject

// Frequency data class based on reference project
data class HabitFrequency(
    val numerator: Int,
    val denominator: Int
) {
    fun toDisplayString(): String {
        return when {
            numerator == 1 && denominator == 1 -> "Every day"
            numerator == 1 && denominator > 1 -> "Every $denominator days"
            denominator == 7 -> "$numerator times per week"
            denominator == 30 -> "$numerator times per month"
            else -> "$numerator times per $denominator days"
        }
    }
    
    companion object {
        val DAILY = HabitFrequency(1, 1)
        val WEEKLY = HabitFrequency(1, 7)
        val MONTHLY = HabitFrequency(1, 30)
    }
}

// Reminder state data class based on reference project structure
data class ReminderState(
    val isEnabled: Boolean = false,
    val time: LocalTime = LocalTime.of(8, 0), // Default to 8:00 AM
    val selectedDays: Set<DayOfWeek> = setOf(
        DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY,
        DayOfWeek.THURSDAY, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY
    )
) {
    fun toDisplayString(): String {
        return if (!isEnabled) {
            "Off"
        } else {
            val timeString = String.format("%02d:%02d", time.hour, time.minute)
            when {
                selectedDays.size == 7 -> "Daily at $timeString"
                selectedDays.size == 1 -> {
                    val dayName = selectedDays.first().name.lowercase().replaceFirstChar { it.uppercase() }
                    "$dayName at $timeString"
                }
                else -> {
                    val dayNames = selectedDays.sortedBy { it.value }.map { 
                        when (it) {
                            DayOfWeek.MONDAY -> "Mon"
                            DayOfWeek.TUESDAY -> "Tue"
                            DayOfWeek.WEDNESDAY -> "Wed"
                            DayOfWeek.THURSDAY -> "Thu"
                            DayOfWeek.FRIDAY -> "Fri"
                            DayOfWeek.SATURDAY -> "Sat"
                            DayOfWeek.SUNDAY -> "Sun"
                        }
                    }.joinToString(", ")
                    "$dayNames at $timeString"
                }
            }
        }
    }
}

data class CreateHabitUiState(
    val availableSections: List<HabitSection> = emptyList(),
    val selectedSection: HabitSection? = null,
    val selectedColor: Int = 0xFF81E6D9.toInt(), // Default accent color from style guide
    val selectedFrequency: HabitFrequency = HabitFrequency.DAILY,
    val enhancedFrequency: EnhancedFrequency = EnhancedFrequency.DAILY,
    val reminderState: ReminderState = ReminderState(),
    val isLoading: Boolean = false,
    val showSectionSelector: Boolean = false,
    val showReminderDialog: Boolean = false,
    // Edit mode properties
    val isEditMode: Boolean = false,
    val editingHabitId: Long? = null,
    val isLoadingHabit: Boolean = false,
    val loadedHabitData: com.example.habits9.data.Habit? = null
)

@HiltViewModel
class CreateHabitViewModel @Inject constructor(
    private val habitSectionRepository: HabitSectionRepository,
    private val habitRepository: com.example.habits9.data.HabitRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CreateHabitUiState())
    val uiState: StateFlow<CreateHabitUiState> = _uiState.asStateFlow()

    init {
        loadSections()
    }

    private fun loadSections() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            habitSectionRepository.getAllHabitSections().collect { sections ->
                _uiState.value = _uiState.value.copy(
                    availableSections = sections,
                    isLoading = false,
                    // Set first section as default if none selected and sections available
                    selectedSection = if (_uiState.value.selectedSection == null && sections.isNotEmpty()) {
                        sections.first()
                    } else {
                        _uiState.value.selectedSection
                    },
                    // Update color to match selected section
                    selectedColor = if (_uiState.value.selectedSection == null && sections.isNotEmpty()) {
                        sections.first().color
                    } else {
                        _uiState.value.selectedColor
                    }
                )
            }
        }
    }

    fun showSectionSelector() {
        _uiState.value = _uiState.value.copy(showSectionSelector = true)
    }

    fun hideSectionSelector() {
        _uiState.value = _uiState.value.copy(showSectionSelector = false)
    }

    fun selectSection(section: HabitSection) {
        _uiState.value = _uiState.value.copy(
            selectedSection = section,
            selectedColor = section.color, // Automatically update color to match section
            showSectionSelector = false
        )
    }

    fun updateColor(color: Int) {
        _uiState.value = _uiState.value.copy(selectedColor = color)
    }
    
    fun updateFrequency(frequency: HabitFrequency) {
        _uiState.value = _uiState.value.copy(selectedFrequency = frequency)
    }

    fun updateEnhancedFrequency(frequency: EnhancedFrequency) {
        _uiState.value = _uiState.value.copy(enhancedFrequency = frequency)
    }
    
    fun showReminderDialog() {
        _uiState.value = _uiState.value.copy(showReminderDialog = true)
    }
    
    fun hideReminderDialog() {
        _uiState.value = _uiState.value.copy(showReminderDialog = false)
    }
    
    fun updateReminder(reminderState: ReminderState) {
        _uiState.value = _uiState.value.copy(
            reminderState = reminderState,
            showReminderDialog = false
        )
    }

    /**
     * Initialize the ViewModel for editing an existing habit
     */
    fun initializeForEdit(habitId: Long) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isEditMode = true,
                editingHabitId = habitId,
                isLoadingHabit = true
            )

            try {
                // Load the habit data
                habitRepository.getHabitById(habitId).collect { habit ->
                    // Convert habit data back to UI state
                    val enhancedFreq = EnhancedFrequency.fromDatabaseValues(
                        habit.frequencyType,
                        habit.repeatsEvery,
                        habit.daysOfWeek,
                        habit.dayOfMonth,
                        habit.weekOfMonth,
                        habit.dayOfWeekInMonth
                    )

                    // Find the section for this habit
                    val currentSections = _uiState.value.availableSections
                    val habitSection = currentSections.find { it.id == habit.sectionId }

                    _uiState.value = _uiState.value.copy(
                        selectedColor = habit.color,
                        enhancedFrequency = enhancedFreq,
                        selectedSection = habitSection,
                        isLoadingHabit = false,
                        loadedHabitData = habit
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoadingHabit = false
                )
            }
        }
    }

    /**
     * Get habit data for editing (synchronous method for UI initialization)
     */
    suspend fun getHabitForEdit(habitId: Long): com.example.habits9.data.Habit? {
        return try {
            habitRepository.getHabitById(habitId).first()
        } catch (e: Exception) {
            null
        }
    }

    fun saveHabit(
        name: String,
        question: String,
        notes: String,
        habitType: com.example.habits9.data.HabitType,
        unit: String = "",
        target: String = "",
        targetType: String = "At Least",
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                // Validation
                if (name.isBlank()) {
                    onError("Name cannot be empty")
                    return@launch
                }
                
                val currentState = _uiState.value
                
                // Convert target to double for numerical habits
                val targetValue = if (habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                    target.toDoubleOrNull() ?: 0.0
                } else {
                    0.0
                }
                
                // Convert target type
                val numericalTargetType = if (targetType == "At Most") {
                    com.example.habits9.data.NumericalHabitType.AT_MOST.value
                } else {
                    com.example.habits9.data.NumericalHabitType.AT_LEAST.value
                }
                
                // Get enhanced frequency data
                val frequencyData = currentState.enhancedFrequency.toDatabaseValues()

                if (currentState.isEditMode && currentState.editingHabitId != null) {
                    // Get the existing habit to preserve important properties
                    val existingHabit = habitRepository.getHabitById(currentState.editingHabitId).first()

                    // Update existing habit while preserving important properties
                    val updatedHabit = existingHabit.copy(
                        name = name,
                        description = question,
                        color = currentState.selectedColor,
                        type = habitType.name,
                        targetType = numericalTargetType,
                        targetValue = targetValue,
                        unit = unit,
                        frequencyType = frequencyData.frequencyType,
                        repeatsEvery = frequencyData.repeatsEvery,
                        daysOfWeek = frequencyData.daysOfWeek,
                        dayOfMonth = frequencyData.dayOfMonth,
                        weekOfMonth = frequencyData.weekOfMonth,
                        dayOfWeekInMonth = frequencyData.dayOfWeekInMonth,
                        sectionId = currentState.selectedSection?.id ?: existingHabit.sectionId
                    )

                    // Update in database
                    habitRepository.updateHabit(updatedHabit)
                } else {
                    // Create new habit
                    val newHabit = com.example.habits9.data.Habit(
                        name = name,
                        description = question,
                        color = currentState.selectedColor,
                        type = habitType.name,
                        targetType = numericalTargetType,
                        targetValue = targetValue,
                        unit = unit,
                        sectionId = currentState.selectedSection?.id ?: "",
                        frequencyType = frequencyData.frequencyType,
                        repeatsEvery = frequencyData.repeatsEvery,
                        daysOfWeek = frequencyData.daysOfWeek,
                        dayOfMonth = frequencyData.dayOfMonth,
                        weekOfMonth = frequencyData.weekOfMonth,
                        dayOfWeekInMonth = frequencyData.dayOfWeekInMonth
                    )

                    // Save to database
                    habitRepository.insertHabit(newHabit)
                }
                
                // Navigate back on success
                onSuccess()
                
            } catch (e: Exception) {
                onError("Failed to save habit: ${e.message}")
            }
        }
    }
}